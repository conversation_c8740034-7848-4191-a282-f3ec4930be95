"""
XML to JSON converter for agent event processing using xmltodict.

This module provides utilities to convert raw XML agent events from SQS messages
to clean JSON objects while preserving hierarchy.
"""

import json
from typing import Dict, Any
import xmltodict
from aws_lambda_powertools import Logger

logger = Logger()


class AgentEventXMLParser:
    """Converts XML agent events to clean JSON objects using xmltodict."""

    @classmethod
    def xml_to_json(cls, xml_content: str) -> Dict[str, Any]:
        """
        Convert XML event to clean JSON object preserving hierarchy using xmltodict.

        Args:
            xml_content: Raw XML content from SQS message

        Returns:
            Clean JSON object with hierarchy preserved

        Raises:
            ValueError: If XML parsing fails or required fields are missing
        """
        try:
            # Convert XML to dictionary using xmltodict
            data = xmltodict.parse(
                xml_content,
                process_namespaces=False,  # Disable namespace processing for security
                namespace_separator=':',
                disable_entities=True,  # Disable entity processing
            )
            # Extract the LogEvent content (remove root wrapper)
            if "LogEvent" in data:
                json_data = data["LogEvent"]
            else:
                json_data = data

            # Validate required fields exist
            required_fields = ["timestamp", "agencyOrElement", "agent", "eventType"]
            missing_fields = [f for f in required_fields if f not in json_data]
            if missing_fields:
                raise ValueError(f"Missing required fields: {missing_fields}")

            logger.info(
                "Successfully converted XML to JSON",
                event_type=json_data.get("eventType"),
                agent=json_data.get("agent"),
                agency=json_data.get("agencyOrElement")
            )

            return json_data

        except Exception as e:
            logger.error("XML to JSON conversion failed", error=str(e), xml_preview=xml_content[:500])
            raise ValueError(f"Failed to parse XML: {e}") from e



    @classmethod
    def extract_xml_from_sqs_message(cls, sqs_body: str) -> str:
        """
        Extract XML content from SQS message body with security validations.

        The SQS message might be wrapped in JSON or contain the raw XML.

        Args:
            sqs_body: Raw SQS message body

        Returns:
            Extracted XML content
        """
        try:
            # Try to parse as JSON first (SNS -> SQS pattern)
            message_data = json.loads(sqs_body)

            # Check if it's an SNS message
            if "Message" in message_data:
                xml_content = message_data["Message"]
            else:
                # Direct SQS message with XML
                xml_content = sqs_body

        except json.JSONDecodeError:
            # Raw XML content
            xml_content = sqs_body

        # Additional validation
        xml_content = xml_content.strip()
        if not xml_content:
            raise ValueError("Empty XML content after extraction")

        return xml_content
