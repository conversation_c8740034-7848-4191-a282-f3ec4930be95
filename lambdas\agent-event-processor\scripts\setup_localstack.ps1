# PowerShell setup script for LocalStack environment
# This script creates the necessary AWS resources for local testing

$ErrorActionPreference = "Stop"

# Configuration
$LOCALSTACK_ENDPOINT = "http://localhost:4566"
$AWS_REGION = "us-east-1"
$QUEUE_NAME = "agent-events-queue"
$DLQ_NAME = "agent-events-dlq"
$SECRET_NAME = "agent-event-processor/database"

Write-Host "Setting up LocalStack environment for Agent Event Processor..."

# Configure AWS CLI for LocalStack
$env:AWS_ACCESS_KEY_ID = "test"
$env:AWS_SECRET_ACCESS_KEY = "test"
$env:AWS_DEFAULT_REGION = $AWS_REGION

# Wait for LocalStack to be ready
Write-Host "Waiting for LocalStack to be ready..."
do {
    try {
        $response = Invoke-WebRequest -Uri "$LOCALSTACK_ENDPOINT/_localstack/health" -UseBasicParsing
        $health = $response.Content | ConvertFrom-Json
        if ($health.services.sqs -eq "running") {
            break
        }
    }
    catch {
        Write-Host "Waiting for LocalStack..."
        Start-Sleep -Seconds 2
    }
} while ($true)

Write-Host "LocalStack is ready!"

# Create SQS Dead Letter Queue
Write-Host "Creating SQS Dead Letter Queue..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs create-queue --queue-name $DLQ_NAME --region $AWS_REGION

$DLQ_URL = aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-url --queue-name $DLQ_NAME --region $AWS_REGION --query 'QueueUrl' --output text

$DLQ_ARN = aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-attributes --queue-url $DLQ_URL --attribute-names QueueArn --region $AWS_REGION --query 'Attributes.QueueArn' --output text

Write-Host "Created DLQ: $DLQ_ARN"

# Create main SQS Queue with DLQ configuration
Write-Host "Creating main SQS Queue..."
$redrive_policy = @{
    deadLetterTargetArn = $DLQ_ARN
    maxReceiveCount = 3
} | ConvertTo-Json -Compress

aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs create-queue --queue-name $QUEUE_NAME --attributes "RedrivePolicy=$redrive_policy" --region $AWS_REGION

$QUEUE_URL = aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-url --queue-name $QUEUE_NAME --region $AWS_REGION --query 'QueueUrl' --output text

Write-Host "Created Queue: $QUEUE_URL"

# Create Secrets Manager secret for database
Write-Host "Creating database secret..."
$secret_value = @{
    host = "postgres"
    port = 5432
    database = "test_analytics"
    username = "test_user"
    password = "test_password"
} | ConvertTo-Json -Compress

aws --endpoint-url=$LOCALSTACK_ENDPOINT secretsmanager create-secret --name $SECRET_NAME --description "Database credentials for agent event processor" --secret-string $secret_value --region $AWS_REGION

Write-Host "Created secret: $SECRET_NAME"

# Create IAM role for Lambda
Write-Host "Creating IAM role..."
$assume_role_policy = @{
    Version = "2012-10-17"
    Statement = @(
        @{
            Effect = "Allow"
            Principal = @{
                Service = "lambda.amazonaws.com"
            }
            Action = "sts:AssumeRole"
        }
    )
} | ConvertTo-Json -Depth 10 -Compress

try {
    aws --endpoint-url=$LOCALSTACK_ENDPOINT iam create-role --role-name lambda-role --assume-role-policy-document $assume_role_policy --region $AWS_REGION
}
catch {
    Write-Host "IAM role may already exist"
}

Write-Host "LocalStack setup completed!"
Write-Host "SQS Queue URL: $QUEUE_URL"
Write-Host "Database Secret: $SECRET_NAME"