{"tests/unit/test_event_processor.py": true, "tests/unit/test_lambda_function.py": true, "tests/unit/test_sqs_service.py": true, "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_login_event_full_invocation": true, "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_acd_login_event_full_invocation": true, "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_all_event_types_batch": true, "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_partial_batch_failure": true, "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_sns_wrapped_message": true, "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_empty_records": true}