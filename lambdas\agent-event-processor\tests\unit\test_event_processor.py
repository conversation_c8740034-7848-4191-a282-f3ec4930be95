"""Unit tests for EventProcessor."""

import pytest
from datetime import datetime
from unittest.mock import Mock, patch

from agent_event_processor.models.events import AgentEvent, EventType
from agent_event_processor.models.database import DimensionKeys
from agent_event_processor.services.event_processor import EventProcessor
from agent_event_processor.config.settings import Settings


class TestEventProcessor:
    """Test cases for EventProcessor class."""

    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        settings = Mock(spec=Settings)
        settings.database.host = "localhost"
        settings.database.port = 5432
        settings.database.name = "test_db"
        settings.database.user = "test_user"
        settings.database.password = "test_pass"
        return settings

    @pytest.fixture
    def mock_db_service(self):
        """Mock database service."""
        return Mock()

    @pytest.fixture
    def mock_dimension_manager(self):
        """Mock dimension manager."""
        return Mock()

    @pytest.fixture
    def mock_fact_manager(self):
        """Mock fact manager."""
        return Mock()

    @pytest.fixture
    def event_processor(self, mock_settings, mock_db_service, mock_dimension_manager, mock_fact_manager):
        """Create EventProcessor instance with mocked dependencies."""
        with patch('agent_event_processor.services.event_processor.DatabaseService', return_value=mock_db_service), \
             patch('agent_event_processor.services.event_processor.DimensionTableManager', return_value=mock_dimension_manager), \
             patch('agent_event_processor.services.event_processor.FactTableManager', return_value=mock_fact_manager):
            return EventProcessor(mock_settings)

    @pytest.fixture
    def sample_login_event(self):
        """Sample login event for testing."""
        return AgentEvent(
            timestamp=datetime(2025, 2, 5, 11, 44, 18),
            eventType=EventType.LOGIN,
            agencyOrElement="Brandon911",
            agent="test-agent-001",
            event_data={
                "mediaLabel": "_ML_194D5ECDE50C0001C46A@BrandonMB",
                "uri": "tel:+2045553006",
                "agentRole": "Rural - CT",
                "reason": "normal",
                "operatorId": "OP001",
                "workstation": "WS-001",
                "deviceName": "Headset"
            }
        )

    def test_resolve_dimension_keys(self, event_processor, sample_login_event, mock_dimension_manager):
        """Test dimension key resolution."""
        # Setup mocks
        mock_dimension_manager.get_or_create_tenant_key.return_value = 1
        mock_dimension_manager.upsert_agent_dimension.return_value = 2
        mock_dimension_manager.get_or_create_queue_key.return_value = None

        with patch('agent_event_processor.services.event_processor.get_tenant_timezone', return_value='UTC'), \
             patch('agent_event_processor.services.event_processor.convert_to_tenant_timezone', return_value=sample_login_event.timestamp), \
             patch('agent_event_processor.services.event_processor.generate_dimension_keys', return_value=(20250205, 114418)):

            # Execute
            result = event_processor._resolve_dimension_keys(sample_login_event)

            # Verify
            assert isinstance(result, DimensionKeys)
            assert result.tenant_key == 1
            assert result.agent_key == 2
            assert result.date_key == 20250205
            assert result.time_key == 114418
            assert result.queue_key is None

    def test_transform_to_fact_data(self, event_processor, sample_login_event):
        """Test event transformation to fact data."""
        # Setup
        dimension_keys = DimensionKeys(
            tenant_key=1,
            agent_key=2,
            date_key=20250205,
            time_key=114418,
            queue_key=None
        )

        with patch('agent_event_processor.services.event_processor.convert_to_tenant_timezone', return_value=sample_login_event.timestamp), \
             patch('agent_event_processor.services.event_processor.get_tenant_timezone', return_value='UTC'):

            # Execute
            result = event_processor._transform_to_fact_data(sample_login_event, dimension_keys)

            # Verify
            assert result["tenant_key"] == 1
            assert result["agent_key"] == 2
            assert result["date_key"] == 20250205
            assert result["time_key"] == 114418
            assert result["event_type"] == "Login"
            assert result["reason_code"] == "normal"
            assert result["media_label"] == "_ML_194D5ECDE50C0001C46A@BrandonMB"
            assert result["workstation"] == "WS-001"
            assert result["device_name"] == "Headset"

    def test_insert_fact_record_success(self, event_processor, mock_fact_manager):
        """Test successful fact record insertion."""
        # Setup
        fact_data = {
            "tenant_key": 1,
            "agent_key": 2,
            "event_type": "Login"
        }
        mock_fact_manager.insert_agent_event.return_value = True

        # Execute
        event_processor._insert_fact_record(fact_data)

        # Verify
        mock_fact_manager.insert_agent_event.assert_called_once_with(fact_data)

    def test_insert_fact_record_duplicate(self, event_processor, mock_fact_manager):
        """Test fact record insertion with duplicate."""
        # Setup
        fact_data = {
            "tenant_key": 1,
            "agent_key": 2,
            "event_type": "Login"
        }
        mock_fact_manager.insert_agent_event.return_value = False

        # Execute
        event_processor._insert_fact_record(fact_data)

        # Verify
        mock_fact_manager.insert_agent_event.assert_called_once_with(fact_data)

    @patch('agent_event_processor.services.event_processor.time.time')
    def test_process_single_event_success(self, mock_time, event_processor, sample_login_event, mock_fact_manager):
        """Test successful single event processing."""
        # Setup
        mock_time.return_value = 1000.0
        mock_context = Mock()
        
        with patch.object(event_processor, '_resolve_dimension_keys') as mock_resolve, \
             patch.object(event_processor, '_transform_to_fact_data') as mock_transform, \
             patch.object(event_processor, '_insert_fact_record') as mock_insert:
            
            mock_resolve.return_value = DimensionKeys(
                tenant_key=1, agent_key=2, date_key=20250205, time_key=114418, queue_key=None
            )
            mock_transform.return_value = {"tenant_key": 1, "event_type": "Login"}

            # Execute
            event_processor.process_single_event(sample_login_event, mock_context)

            # Verify
            mock_resolve.assert_called_once_with(sample_login_event)
            mock_transform.assert_called_once()
            mock_insert.assert_called_once()

    def test_process_single_event_failure(self, event_processor, sample_login_event):
        """Test single event processing failure."""
        # Setup
        mock_context = Mock()
        
        with patch.object(event_processor, '_resolve_dimension_keys', side_effect=Exception("Database error")):
            
            # Execute & Verify
            with pytest.raises(Exception, match="Database error"):
                event_processor.process_single_event(sample_login_event, mock_context)
