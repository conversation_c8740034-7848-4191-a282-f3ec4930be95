"""
Database utility functions for Redshift connectivity and credential management.

This module provides utilities for secure database credential retrieval
and connection management optimized for Lambda execution.
"""

import json
from functools import lru_cache
from typing import Dict, Any

import boto3
from botocore.exceptions import ClientError
from aws_lambda_powertools import Logger

from ..models.database import DatabaseCredentials
from ..config.settings import DatabaseSettings

logger = Logger()


@lru_cache(maxsize=1)
def get_secrets_manager_client() -> boto3.client:
    """
    Get cached Secrets Manager client.

    Returns:
        boto3.client: Cached Secrets Manager client.
    """
    return boto3.client("secretsmanager")


def get_db_credentials(secret_name: str) -> DatabaseCredentials:
    """
    Retrieve database credentials from AWS Secrets Manager.

    Args:
        secret_name: Name of the secret containing database credentials.

    Returns:
        DatabaseCredentials: Validated database credentials.

    Raises:
        ValueError: If credentials are invalid or missing.
        ClientError: If secret retrieval fails.
    """
    try:
        logger.debug("Retrieving database credentials", secret_name=secret_name)

        client = get_secrets_manager_client()

        response = client.get_secret_value(SecretId=secret_name)
        secret_data = json.loads(response["SecretString"])

        # Validate and create credentials object
        credentials = DatabaseCredentials(**secret_data)

        logger.info(
            "Database credentials retrieved successfully",
            secret_name=secret_name,
            host=credentials.host,
            database=credentials.database,
            username=credentials.username,
        )

        return credentials

    except ClientError as e:
        error_code = e.response["Error"]["Code"]
        logger.error(
            "Failed to retrieve database credentials",
            secret_name=secret_name,
            error_code=error_code,
            error=str(e),
        )
        raise

    except (json.JSONDecodeError, KeyError, TypeError) as e:
        logger.error("Invalid secret format", secret_name=secret_name, error=str(e))
        raise ValueError(f"Invalid secret format in {secret_name}: {e}") from e


def build_connection_string(credentials: DatabaseCredentials) -> str:
    """
    Build PostgreSQL connection string from credentials.

    Args:
        credentials: Database credentials.

    Returns:
        str: PostgreSQL connection string.
    """
    connection_string = (
        f"postgresql://{credentials.username}:{credentials.password}"
        f"@{credentials.host}:{credentials.port}/{credentials.database}"
    )

    logger.debug(
        "Built connection string",
        host=credentials.host,
        port=credentials.port,
        database=credentials.database,
        username=credentials.username,
    )

    return connection_string


def get_connection_params(credentials: DatabaseCredentials) -> Dict[str, Any]:
    """
    Get psycopg2 connection parameters from credentials.

    Args:
        credentials: Database credentials.

    Returns:
        Dict[str, Any]: Connection parameters for psycopg2.
    """
    params = {
        "host": credentials.host,
        "port": credentials.port,
        "database": credentials.database,
        "user": credentials.username,
        "password": credentials.password,
        "sslmode": "require",
        "connect_timeout": 30,
        "application_name": "agent-event-processor",
        "keepalives_idle": 600,
        "keepalives_interval": 30,
        "keepalives_count": 3,
    }

    logger.debug(
        "Generated connection parameters",
        host=params["host"],
        port=params["port"],
        database=params["database"],
        user=params["user"],
        application_name=params["application_name"],
    )

    return params


def get_connection_params_from_settings(settings: DatabaseSettings) -> Dict[str, Any]:
    """
    Get database connection parameters from settings.

    Args:
        settings: Database configuration settings.

    Returns:
        Dict containing connection parameters for psycopg2.

    Raises:
        Exception: If unable to retrieve connection parameters.
    """
    try:
        # Check if using direct connection settings (for local development)
        if settings.host and settings.port and settings.name and settings.user and settings.password:
            connection_params = {
                "host": settings.host,
                "port": settings.port,
                "database": settings.name,
                "user": settings.user,
                "password": settings.password,
                "connect_timeout": settings.connection_timeout,
                "options": f"-c statement_timeout={settings.query_timeout}s",
                "application_name": "agent-event-processor",
            }

            logger.info(
                "Using direct database connection parameters",
                host=settings.host,
                port=settings.port,
                database=settings.name,
                user=settings.user,
            )

        else:
            # Get credentials from AWS Secrets Manager
            credentials = get_db_credentials(settings.secret_name)

            connection_params = {
                "host": credentials.host,
                "port": credentials.port,
                "database": credentials.database,
                "user": credentials.username,
                "password": credentials.password,
                "connect_timeout": settings.connection_timeout,
                "options": f"-c statement_timeout={settings.query_timeout}s",
                "sslmode": "require",
                "application_name": "agent-event-processor",
                "keepalives_idle": 600,
                "keepalives_interval": 30,
                "keepalives_count": 3,
            }

            logger.info(
                "Using AWS Secrets Manager database connection parameters",
                host=credentials.host,
                port=credentials.port,
                database=credentials.database,
                user=credentials.username,
            )

        return connection_params

    except Exception as e:
        logger.error(
            "Failed to get database connection parameters",
            secret_name=settings.secret_name,
            error=str(e),
            exc_info=True,
        )
        raise


def validate_connection_health(connection) -> bool:
    """
    Validate database connection health.

    Args:
        connection: Database connection to validate.

    Returns:
        bool: True if connection is healthy, False otherwise.
    """
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()

        is_healthy = result is not None and result[0] == 1

        logger.debug("Connection health check completed", is_healthy=is_healthy)

        return is_healthy

    except Exception as e:
        logger.warning("Connection health check failed", error=str(e))
        return False
