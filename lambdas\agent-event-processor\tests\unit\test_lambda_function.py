"""
Unit tests for Lambda function handler.

This module tests the main Lambda handler with mocked dependencies
and various event scenarios.
"""

import json
import pytest
from unittest.mock import Mock, patch, MagicMock

from src.agent_event_processor.lambda_function import lambda_handler, get_event_processor


class TestLambdaHandler:
    """Test suite for Lambda handler function."""
    
    @patch('src.agent_event_processor.lambda_function.get_event_processor')
    @patch('src.agent_event_processor.lambda_function.get_metrics_collector')
    def test_successful_batch_processing(
        self, 
        mock_get_metrics, 
        mock_get_processor,
        mock_sqs_event,
        mock_lambda_context
    ):
        """Test successful processing of SQS batch."""
        # Mock processor and metrics
        mock_processor = Mock()
        mock_processor.process_batch.return_value = {
            "successful_count": 1,
            "failed_count": 0,
            "processing_time_ms": 100.0,
            "batch_item_failures": [],
            "failed_message_ids": []
        }
        mock_get_processor.return_value = mock_processor
        
        mock_metrics = Mock()
        mock_get_metrics.return_value = mock_metrics
        
        # Call handler
        response = lambda_handler(mock_sqs_event, mock_lambda_context)
        
        # Verify response
        assert response["statusCode"] == 200
        
        body = json.loads(response["body"])
        assert body["successful_count"] == 1
        assert body["failed_count"] == 0
        assert "correlation_id" in body
        
        # Verify processor was called
        mock_processor.process_batch.assert_called_once()
        
        # Verify metrics were published
        mock_metrics.put_metric.assert_called()
        mock_metrics.flush_metrics.assert_called()
    
    @patch('src.agent_event_processor.lambda_function.get_event_processor')
    @patch('src.agent_event_processor.lambda_function.get_metrics_collector')
    def test_partial_batch_failure(
        self, 
        mock_get_metrics, 
        mock_get_processor,
        mock_sqs_event,
        mock_lambda_context
    ):
        """Test handling of partial batch failures."""
        # Mock processor with partial failure
        mock_processor = Mock()
        mock_processor.process_batch.return_value = {
            "successful_count": 1,
            "failed_count": 1,
            "processing_time_ms": 150.0,
            "batch_item_failures": [{"itemIdentifier": "failed-msg-1"}],
            "failed_message_ids": ["failed-msg-1"]
        }
        mock_get_processor.return_value = mock_processor
        
        mock_metrics = Mock()
        mock_get_metrics.return_value = mock_metrics
        
        # Call handler
        response = lambda_handler(mock_sqs_event, mock_lambda_context)
        
        # Verify response includes batch failures
        assert response["statusCode"] == 200
        assert "batchItemFailures" in response
        assert len(response["batchItemFailures"]) == 1
        assert response["batchItemFailures"][0]["itemIdentifier"] == "failed-msg-1"
        
        # Verify metrics include failure counts
        mock_metrics.put_metric.assert_any_call("FailedRecords", 1)
        mock_metrics.put_metric.assert_any_call("BatchPartialFailure", 1)
    
    @patch('src.agent_event_processor.lambda_function.get_event_processor')
    @patch('src.agent_event_processor.lambda_function.get_metrics_collector')
    def test_processing_exception(
        self, 
        mock_get_metrics, 
        mock_get_processor,
        mock_sqs_event,
        mock_lambda_context
    ):
        """Test handling of processing exceptions."""
        # Mock processor to raise exception
        mock_processor = Mock()
        mock_processor.process_batch.side_effect = Exception("Processing failed")
        mock_get_processor.return_value = mock_processor
        
        mock_metrics = Mock()
        mock_get_metrics.return_value = mock_metrics
        
        # Call handler and expect exception
        with pytest.raises(Exception, match="Processing failed"):
            lambda_handler(mock_sqs_event, mock_lambda_context)
        
        # Verify error metrics were published
        mock_metrics.put_metric.assert_any_call("LambdaErrors", 1)
        mock_metrics.flush_metrics.assert_called()
    
    def test_empty_records(self, mock_lambda_context):
        """Test handling of empty SQS records."""
        empty_event = {"Records": []}
        
        with patch('src.agent_event_processor.lambda_function.get_event_processor') as mock_get_processor:
            mock_processor = Mock()
            mock_processor.process_batch.return_value = {
                "successful_count": 0,
                "failed_count": 0,
                "processing_time_ms": 10.0,
                "batch_item_failures": [],
                "failed_message_ids": []
            }
            mock_get_processor.return_value = mock_processor
            
            response = lambda_handler(empty_event, mock_lambda_context)
            
            assert response["statusCode"] == 200
            body = json.loads(response["body"])
            assert body["successful_count"] == 0
            assert body["failed_count"] == 0


class TestSingletonInstances:
    """Test suite for singleton instance management."""
    
    @patch('src.agent_event_processor.lambda_function._event_processor', None)
    @patch('src.agent_event_processor.lambda_function.EventProcessor')
    @patch('src.agent_event_processor.lambda_function.get_settings')
    @patch('src.agent_event_processor.lambda_function.get_metrics_collector')
    def test_event_processor_singleton(
        self, 
        mock_get_metrics, 
        mock_get_settings, 
        mock_event_processor_class
    ):
        """Test that event processor is created as singleton."""
        mock_settings = Mock()
        mock_get_settings.return_value = mock_settings
        
        mock_metrics = Mock()
        mock_get_metrics.return_value = mock_metrics
        
        mock_processor_instance = Mock()
        mock_event_processor_class.return_value = mock_processor_instance
        
        # First call should create instance
        processor1 = get_event_processor()
        assert processor1 == mock_processor_instance
        mock_event_processor_class.assert_called_once_with(mock_settings, mock_metrics)
        
        # Second call should return same instance
        processor2 = get_event_processor()
        assert processor2 == mock_processor_instance
        # Constructor should not be called again
        assert mock_event_processor_class.call_count == 1

