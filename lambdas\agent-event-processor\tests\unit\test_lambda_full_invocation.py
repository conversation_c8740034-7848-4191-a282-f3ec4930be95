"""
Comprehensive unit tests for Lambda function with full invocation testing.

This module tests the complete lambda handler with all event types
and various scenarios to ensure production readiness.
"""

import json
import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from src.agent_event_processor.lambda_function import lambda_handler


class TestLambdaFullInvocation:
    """Test suite for complete Lambda function invocation."""

    @pytest.fixture
    def mock_lambda_context(self):
        """Mock Lambda context."""
        context = Mock()
        context.memory_limit_in_mb = 512
        context.get_remaining_time_in_millis.return_value = 300000  # 5 minutes
        context.function_name = "agent-event-processor"
        context.function_version = "1"
        context.invoked_function_arn = "arn:aws:lambda:us-east-1:123456789012:function:agent-event-processor"
        return context

    @pytest.fixture
    def login_event_xml(self):
        """Sample Login event XML."""
        return """<LogEvent>
            <timestamp>2024-01-15T10:30:00Z</timestamp>
            <eventType>Login</eventType>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>john.doe</agent>
            <mediaLabel>Audio_1</mediaLabel>
            <uri>tel:+2045553006</uri>
            <agentRole>Rural - CT</agentRole>
            <workstation>OP6</workstation>
            <reason>normal</reason>
        </LogEvent>"""

    @pytest.fixture
    def logout_event_xml(self):
        """Sample Logout event XML."""
        return """<LogEvent>
            <timestamp>2024-01-15T10:35:00Z</timestamp>
            <eventType>Logout</eventType>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>john.doe</agent>
            <reason>normal</reason>
        </LogEvent>"""

    @pytest.fixture
    def acd_login_event_xml(self):
        """Sample ACD Login event XML."""
        return """<LogEvent>
            <timestamp>2024-01-15T10:32:00Z</timestamp>
            <eventType>ACDLogin</eventType>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>john.doe</agent>
            <ringGroupName>911 Queue</ringGroupName>
            <ringGroupUri>sip:<EMAIL></ringGroupUri>
        </LogEvent>"""

    @pytest.fixture
    def agent_available_event_xml(self):
        """Sample Agent Available event XML."""
        return """<LogEvent>
            <timestamp>2024-01-15T10:33:00Z</timestamp>
            <eventType>AgentAvailable</eventType>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>john.doe</agent>
        </LogEvent>"""

    @pytest.fixture
    def agent_busied_out_event_xml(self):
        """Sample Agent Busied Out event XML."""
        return """<LogEvent>
            <timestamp>2024-01-15T10:34:00Z</timestamp>
            <eventType>AgentBusiedOut</eventType>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>john.doe</agent>
            <reason>break</reason>
        </LogEvent>"""

    @pytest.fixture
    def acd_logout_event_xml(self):
        """Sample ACD Logout event XML."""
        return """<LogEvent>
            <timestamp>2024-01-15T10:36:00Z</timestamp>
            <eventType>ACDLogout</eventType>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>john.doe</agent>
            <ringGroupName>911 Queue</ringGroupName>
        </LogEvent>"""

    def create_sqs_event(self, xml_content, message_id="test-msg-1"):
        """Create SQS event with XML content."""
        return {
            "Records": [
                {
                    "messageId": message_id,
                    "receiptHandle": f"receipt-{message_id}",
                    "body": xml_content,
                    "attributes": {
                        "ApproximateReceiveCount": "1",
                        "SentTimestamp": "1642248600000",
                        "SenderId": "AIDAIENQZJOLO23YVJ4VO",
                        "ApproximateFirstReceiveTimestamp": "1642248600000"
                    },
                    "messageAttributes": {},
                    "md5OfBody": "test-md5",
                    "eventSource": "aws:sqs",
                    "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:agent-events-queue",
                    "awsRegion": "us-east-1"
                }
            ]
        }

    @patch('src.agent_event_processor.lambda_function.get_event_processor')
    def test_login_event_full_invocation(self, mock_get_processor, login_event_xml, mock_lambda_context):
        """Test complete Login event processing."""
        # Mock the event processor
        mock_processor = Mock()
        mock_get_processor.return_value = mock_processor

        # Create SQS event
        sqs_event = self.create_sqs_event(login_event_xml)

        # Invoke lambda
        response = lambda_handler(sqs_event, mock_lambda_context)

        # Verify response
        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["successful_count"] == 1
        assert body["failed_count"] == 0
        assert "batchItemFailures" not in response

        # Verify processor was called
        mock_processor.process_single_event.assert_called_once()
        
        # Verify the event was properly parsed
        call_args = mock_processor.process_single_event.call_args[0]
        agent_event = call_args[0]
        assert agent_event.event_type.value == "Login"
        assert agent_event.agent == "john.doe"
        assert agent_event.agency_or_element == "Brandon911"

    @patch('src.agent_event_processor.lambda_function.get_event_processor')
    def test_acd_login_event_full_invocation(self, mock_get_processor, acd_login_event_xml, mock_lambda_context):
        """Test complete ACD Login event processing."""
        mock_processor = Mock()
        mock_get_processor.return_value = mock_processor

        sqs_event = self.create_sqs_event(acd_login_event_xml)
        response = lambda_handler(sqs_event, mock_lambda_context)

        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["successful_count"] == 1
        assert body["failed_count"] == 0

        # Verify ACD-specific fields
        call_args = mock_processor.process_single_event.call_args[0]
        agent_event = call_args[0]
        assert agent_event.event_type.value == "ACDLogin"
        assert agent_event.get_ring_group_name() == "911 Queue"

    @patch('src.agent_event_processor.lambda_function.get_event_processor')
    def test_all_event_types_batch(self, mock_get_processor, mock_lambda_context,
                                   login_event_xml, logout_event_xml, acd_login_event_xml,
                                   agent_available_event_xml, agent_busied_out_event_xml, acd_logout_event_xml):
        """Test batch processing with all event types."""
        mock_processor = Mock()
        mock_get_processor.return_value = mock_processor

        # Create batch with all event types
        sqs_event = {
            "Records": [
                {"messageId": "msg-1", "receiptHandle": "receipt-1", "body": login_event_xml},
                {"messageId": "msg-2", "receiptHandle": "receipt-2", "body": logout_event_xml},
                {"messageId": "msg-3", "receiptHandle": "receipt-3", "body": acd_login_event_xml},
                {"messageId": "msg-4", "receiptHandle": "receipt-4", "body": agent_available_event_xml},
                {"messageId": "msg-5", "receiptHandle": "receipt-5", "body": agent_busied_out_event_xml},
                {"messageId": "msg-6", "receiptHandle": "receipt-6", "body": acd_logout_event_xml},
            ]
        }

        response = lambda_handler(sqs_event, mock_lambda_context)

        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["successful_count"] == 6
        assert body["failed_count"] == 0

        # Verify all events were processed
        assert mock_processor.process_single_event.call_count == 6

    @patch('src.agent_event_processor.lambda_function.get_event_processor')
    def test_partial_batch_failure(self, mock_get_processor, login_event_xml, mock_lambda_context):
        """Test partial batch failure handling."""
        mock_processor = Mock()
        mock_get_processor.return_value = mock_processor

        # Make second call fail
        mock_processor.process_single_event.side_effect = [None, Exception("Processing failed"), None]

        # Create batch with valid and invalid events
        sqs_event = {
            "Records": [
                {"messageId": "msg-1", "receiptHandle": "receipt-1", "body": login_event_xml},
                {"messageId": "msg-2", "receiptHandle": "receipt-2", "body": "invalid xml"},
                {"messageId": "msg-3", "receiptHandle": "receipt-3", "body": login_event_xml},
            ]
        }

        response = lambda_handler(sqs_event, mock_lambda_context)

        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["successful_count"] == 2
        assert body["failed_count"] == 1

        # Verify batch item failures for SQS
        assert "batchItemFailures" in response
        assert len(response["batchItemFailures"]) == 1
        assert response["batchItemFailures"][0]["itemIdentifier"] == "msg-2"

    @patch('src.agent_event_processor.lambda_function.get_event_processor')
    def test_sns_wrapped_message(self, mock_get_processor, login_event_xml, mock_lambda_context):
        """Test processing SNS-wrapped SQS message."""
        mock_processor = Mock()
        mock_get_processor.return_value = mock_processor

        # Wrap XML in SNS message format
        sns_message = {
            "Type": "Notification",
            "MessageId": "sns-msg-id",
            "TopicArn": "arn:aws:sns:us-east-1:123456789012:agent-events",
            "Message": login_event_xml,
            "Timestamp": "2024-01-15T10:30:00.000Z"
        }

        sqs_event = self.create_sqs_event(json.dumps(sns_message))
        response = lambda_handler(sqs_event, mock_lambda_context)

        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["successful_count"] == 1
        assert body["failed_count"] == 0

    @patch('src.agent_event_processor.lambda_function.get_event_processor')
    def test_metrics_configuration(self, mock_get_processor, login_event_xml, mock_lambda_context):
        """Test that metrics are only emitted when configured."""
        mock_processor = Mock()
        mock_get_processor.return_value = mock_processor

        with patch('src.agent_event_processor.lambda_function.get_settings') as mock_settings:
            # Test with custom metrics disabled
            settings = Mock()
            settings.enable_custom_metrics = False
            settings.enable_business_metrics = True
            mock_settings.return_value = settings

            sqs_event = self.create_sqs_event(login_event_xml)
            response = lambda_handler(sqs_event, mock_lambda_context)

            assert response["statusCode"] == 200
            # Should still process successfully even with metrics disabled

    def test_empty_records(self, mock_lambda_context):
        """Test handling of empty SQS records."""
        sqs_event = {"Records": []}
        response = lambda_handler(sqs_event, mock_lambda_context)

        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["successful_count"] == 0
        assert body["failed_count"] == 0
