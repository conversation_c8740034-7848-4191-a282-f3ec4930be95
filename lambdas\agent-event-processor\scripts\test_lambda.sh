#!/bin/bash

# Test script for Agent Event Processor Lambda
# This script sends test messages to SQS and monitors Lambda execution

set -e

# Configuration
LOCALSTACK_ENDPOINT="http://localhost:4566"
AWS_REGION="us-east-1"
QUEUE_NAME="agent-events-queue"

echo "Testing Agent Event Processor Lambda..."

# Configure AWS CLI for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=$AWS_REGION

# Get queue URL
QUEUE_URL=$(aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-url \
    --queue-name $QUEUE_NAME \
    --region $AWS_REGION \
    --query 'QueueUrl' --output text)

echo "Queue URL: $QUEUE_URL"

# Test XML message
TEST_XML='<LogEvent xmlns="http://solacom.com/Logging">
<timestamp>2025-02-05T11:44:18.031Z</timestamp>
<agencyOrElement>Brandon911</agencyOrElement>
<agent>test-agent</agent>
<eventType>Login</eventType>
<login>
<mediaLabel>_ML_194D5ECDE50C0001C46A@BrandonMB</mediaLabel>
<uri>tel:+2045553006</uri>
<agentRole>Rural - CT</agentRole>
<reason>normal</reason>
</login>
</LogEvent>'

# Send test message to SQS
echo "Sending test message to SQS..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs send-message \
    --queue-url $QUEUE_URL \
    --message-body "{\"Message\": \"$TEST_XML\"}" \
    --region $AWS_REGION

echo "Test message sent!"

# Wait a moment for processing
echo "Waiting for Lambda processing..."
sleep 5

# Check Lambda logs
echo "Checking Lambda logs..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT logs describe-log-groups \
    --region $AWS_REGION || echo "No log groups found"

# Try to get recent logs
LOG_GROUP="/aws/lambda/agent-event-processor"
aws --endpoint-url=$LOCALSTACK_ENDPOINT logs describe-log-streams \
    --log-group-name $LOG_GROUP \
    --region $AWS_REGION \
    --order-by LastEventTime \
    --descending \
    --max-items 1 || echo "No log streams found"

echo "Test completed!"
echo ""
echo "To manually invoke the Lambda function:"
echo "aws --endpoint-url=$LOCALSTACK_ENDPOINT lambda invoke \\"
echo "    --function-name agent-event-processor \\"
echo "    --payload '{\"Records\":[{\"body\":\"{\\\"Message\\\":\\\"$TEST_XML\\\"}\"}]}' \\"
echo "    --region $AWS_REGION \\"
echo "    response.json"
