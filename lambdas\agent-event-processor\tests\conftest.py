"""
Pytest configuration and shared fixtures for Agent Event Processor tests.

This module provides common test fixtures and configuration for unit and
integration tests with proper mocking and test data setup.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, patch
from typing import Dict, Any

import psycopg2
from moto import mock_aws
import boto3

from src.agent_event_processor.config.settings import Settings, DatabaseSettings, AWSSettings
from src.agent_event_processor.models.events import AgentEvent, EventType


@pytest.fixture
def mock_settings() -> Settings:
    """Mock application settings for testing."""
    settings = Settings(
        environment="test",
        database=DatabaseSettings(
            secret_name="test-secret",
            max_connections=2,
            connection_timeout=10
        ),
        aws=AWSSettings(
            region="us-east-1",
            metrics_namespace="Test/AgentEventProcessor"
        )
    )
    return settings


@pytest.fixture
def mock_db_connection():
    """Mock database connection and cursor."""
    conn = Mock(spec=psycopg2.extensions.connection)
    cursor = Mock()
    conn.cursor.return_value.__enter__.return_value = cursor
    conn.cursor.return_value.__exit__.return_value = None
    return conn, cursor


@pytest.fixture
def sample_login_event() -> Dict[str, Any]:
    """Sample login event data for testing with hierarchical structure."""
    return {
        "timestamp": "2024-01-15T10:30:00Z",
        "eventType": "Login",
        "agencyOrElement": "Brandon911",
        "agent": "john.doe",
        "event_data": {
            "mediaLabel": "Audio_1",
            "uri": "tel:+2045553006",
            "agentRole": "Rural - CT",
            "tenantGroup": "Brandon911",
            "operatorId": "6",
            "workstation": "OP6",
            "deviceName": "Headset",
            "reason": "normal"
        }
    }


@pytest.fixture
def sample_logout_event() -> Dict[str, Any]:
    """Sample logout event data for testing with hierarchical structure."""
    return {
        "timestamp": "2024-01-15T18:30:00Z",
        "eventType": "Logout",
        "agencyOrElement": "Brandon911",
        "agent": "john.doe",
        "event_data": {
            "mediaLabel": "Audio_1",
            "uri": "tel:+2045553006",
            "workstation": "OP6",
            "deviceName": "Headset",
            "responseCode": "200"
        }
    }


@pytest.fixture
def sample_acd_login_event() -> Dict[str, Any]:
    """Sample ACD login event data for testing with hierarchical structure."""
    return {
        "timestamp": "2024-01-15T10:35:00Z",
        "eventType": "ACDLogin",
        "agencyOrElement": "Brandon911",
        "agent": "john.doe",
        "event_data": {
            "mediaLabel": "Audio_1",
            "agentUri": "tel:+2045553006",
            "agentRole": "Rural - CT",
            "workstation": "OP6",
            "ringGroupName": "911 Queue",
            "ringGroupUri": "sip:<EMAIL>"
        }
    }


@pytest.fixture
def mock_sqs_event():
    """Mock SQS event structure for Lambda."""
    return {
        "Records": [
            {
                "messageId": "test-message-id-1",
                "receiptHandle": "test-receipt-handle-1",
                "body": '{"Message": "{\\"timestamp\\": \\"2024-01-15T10:30:00Z\\", \\"eventType\\": \\"Login\\", \\"agencyOrElement\\": \\"Brandon911\\", \\"agent\\": \\"john.doe\\"}"}',
                "attributes": {},
                "messageAttributes": {}
            }
        ]
    }


@pytest.fixture
def mock_lambda_context():
    """Mock Lambda context for testing."""
    context = Mock()
    context.aws_request_id = "test-request-id"
    context.function_name = "test-function"
    context.function_version = "1"
    context.memory_limit_in_mb = 256
    context.get_remaining_time_in_millis.return_value = 30000
    return context


@pytest.fixture
def agent_event_login(sample_login_event) -> AgentEvent:
    """Validated AgentEvent object for login."""
    return AgentEvent.model_validate(sample_login_event)


@pytest.fixture
def agent_event_logout(sample_logout_event) -> AgentEvent:
    """Validated AgentEvent object for logout."""
    return AgentEvent.model_validate(sample_logout_event)


@pytest.fixture
def agent_event_acd_login(sample_acd_login_event) -> AgentEvent:
    """Validated AgentEvent object for ACD login."""
    return AgentEvent.model_validate(sample_acd_login_event)


@pytest.fixture
@mock_aws
def mock_secrets_manager():
    """Mock AWS Secrets Manager with test credentials."""
    client = boto3.client('secretsmanager', region_name='us-east-1')

    # Create test secret
    secret_value = {
        "host": "test-redshift.amazonaws.com",
        "port": 5439,
        "database": "test_db",
        "username": "test_user",
        "password": "test_password"
    }

    client.create_secret(
        Name="test-secret",
        SecretString=str(secret_value).replace("'", '"')
    )

    return client


@pytest.fixture
@mock_aws
def mock_cloudwatch():
    """Mock AWS CloudWatch for metrics testing."""
    return boto3.client('cloudwatch', region_name='us-east-1')


# Test markers - define custom markers for pytest
def pytest_configure(config):
    """Configure custom pytest markers."""
    config.addinivalue_line("markers", "unit: mark test as a unit test")
    config.addinivalue_line("markers", "integration: mark test as an integration test")
    config.addinivalue_line("markers", "slow: mark test as slow running")
