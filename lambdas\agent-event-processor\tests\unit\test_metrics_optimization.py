"""
Unit tests for metrics optimization and configuration.

This module tests that custom metrics are only emitted when configured
and that we're not duplicating AWS built-in metrics.
"""

import pytest
from unittest.mock import Mock, patch

from src.agent_event_processor.config.settings import Settings


class TestMetricsOptimization:
    """Test suite for metrics optimization."""

    def test_default_metrics_configuration(self):
        """Test default metrics configuration is cost-optimized."""
        settings = Settings()
        
        # Custom metrics should be disabled by default (expensive)
        assert settings.enable_custom_metrics is False
        
        # Business metrics should be enabled by default (essential)
        assert settings.enable_business_metrics is True

    def test_metrics_configuration_validation(self):
        """Test metrics configuration validation."""
        # Test with custom metrics enabled
        settings = Settings(enable_custom_metrics=True, enable_business_metrics=False)
        assert settings.enable_custom_metrics is True
        assert settings.enable_business_metrics is False

    @patch('src.agent_event_processor.lambda_function.metrics')
    @patch('src.agent_event_processor.lambda_function.get_settings')
    def test_custom_metrics_disabled(self, mock_get_settings, mock_metrics):
        """Test that custom metrics are not emitted when disabled."""
        from src.agent_event_processor.lambda_function import lambda_handler
        
        # Configure settings with custom metrics disabled
        settings = Mock()
        settings.enable_custom_metrics = False
        settings.enable_business_metrics = True
        mock_get_settings.return_value = settings

        # Mock event processor
        with patch('src.agent_event_processor.lambda_function.get_event_processor') as mock_get_processor:
            mock_processor = Mock()
            mock_get_processor.return_value = mock_processor

            # Create test event
            sqs_event = {
                "Records": [
                    {
                        "messageId": "test-msg",
                        "receiptHandle": "test-receipt",
                        "body": """<LogEvent>
                            <timestamp>2024-01-15T10:30:00Z</timestamp>
                            <eventType>Login</eventType>
                            <agencyOrElement>Test911</agencyOrElement>
                            <agent>test.user</agent>
                        </LogEvent>"""
                    }
                ]
            }

            # Mock context
            context = Mock()
            context.memory_limit_in_mb = 512
            context.get_remaining_time_in_millis.return_value = 300000

            # Invoke lambda
            lambda_handler(sqs_event, context)

            # Verify custom metrics were not emitted
            metric_calls = [call for call in mock_metrics.add_metric.call_args_list 
                          if 'PartialBatchFailures' in str(call)]
            assert len(metric_calls) == 0, "Custom metrics should not be emitted when disabled"

    @patch('src.agent_event_processor.lambda_function.metrics')
    @patch('src.agent_event_processor.lambda_function.get_settings')
    def test_business_metrics_enabled(self, mock_get_settings, mock_metrics):
        """Test that business metrics are emitted when enabled."""
        from src.agent_event_processor.lambda_function import lambda_handler
        
        # Configure settings with business metrics enabled
        settings = Mock()
        settings.enable_custom_metrics = False
        settings.enable_business_metrics = True
        mock_get_settings.return_value = settings

        # Mock event processor
        with patch('src.agent_event_processor.lambda_function.get_event_processor') as mock_get_processor:
            mock_processor = Mock()
            mock_get_processor.return_value = mock_processor

            # Create test event
            sqs_event = {
                "Records": [
                    {
                        "messageId": "test-msg",
                        "receiptHandle": "test-receipt",
                        "body": """<LogEvent>
                            <timestamp>2024-01-15T10:30:00Z</timestamp>
                            <eventType>Login</eventType>
                            <agencyOrElement>Test911</agencyOrElement>
                            <agent>test.user</agent>
                        </LogEvent>"""
                    }
                ]
            }

            # Mock context
            context = Mock()
            context.memory_limit_in_mb = 512
            context.get_remaining_time_in_millis.return_value = 300000

            # Invoke lambda
            lambda_handler(sqs_event, context)

            # Verify business metrics were emitted
            metric_calls = [call for call in mock_metrics.add_metric.call_args_list 
                          if 'EventsProcessedSuccessfully' in str(call)]
            assert len(metric_calls) == 1, "Business metrics should be emitted when enabled"

    @patch('src.agent_event_processor.services.event_processor.metrics')
    def test_event_processor_metrics_optimization(self, mock_metrics):
        """Test that event processor only emits essential metrics."""
        from src.agent_event_processor.services.event_processor import EventProcessor
        from src.agent_event_processor.models.events import AgentEvent, EventType
        from src.agent_event_processor.config.settings import Settings
        from datetime import datetime

        # Create settings with business metrics enabled, custom disabled
        settings = Settings(enable_custom_metrics=False, enable_business_metrics=True)

        # Mock dependencies
        with patch('src.agent_event_processor.services.event_processor.DatabaseService'), \
             patch('src.agent_event_processor.services.event_processor.DimensionTableManager'), \
             patch('src.agent_event_processor.services.event_processor.FactTableManager'):
            
            processor = EventProcessor(settings)

            # Create test event
            event_data = {
                "timestamp": datetime.now(),
                "eventType": EventType.LOGIN,
                "agencyOrElement": "Test911",
                "agent": "test.user",
                "event_data": {}
            }
            event = AgentEvent.model_validate(event_data)

            # Mock context
            context = Mock()

            # Mock the internal methods to avoid database calls
            with patch.object(processor, '_resolve_dimension_keys'), \
                 patch.object(processor, '_transform_to_fact_data'), \
                 patch.object(processor, '_insert_fact_record'):
                
                # Process event
                processor.process_single_event(event, context)

                # Verify only business metrics were emitted
                business_metric_calls = [call for call in mock_metrics.add_metric.call_args_list 
                                       if 'EventType_' in str(call)]
                assert len(business_metric_calls) == 1, "Should emit business metrics"

                # Verify no custom processing metrics were emitted
                processing_metric_calls = [call for call in mock_metrics.add_metric.call_args_list 
                                         if 'EventProcessingTime' in str(call)]
                assert len(processing_metric_calls) == 0, "Should not emit custom processing metrics"

    def test_aws_builtin_metrics_documentation(self):
        """Document what AWS already provides to avoid duplication."""
        # This test serves as documentation of what AWS already tracks
        aws_lambda_builtin_metrics = [
            "Invocations",      # Total function invocations
            "Errors",           # Function errors
            "Duration",         # Execution duration
            "Throttles",        # Throttled invocations
            "DeadLetterErrors", # Dead letter queue errors
            "IteratorAge",      # For stream-based invocations
        ]

        aws_sqs_builtin_metrics = [
            "NumberOfMessagesSent",
            "NumberOfMessagesReceived",
            "NumberOfMessagesDeleted",
            "ApproximateNumberOfVisibleMessages",
            "ApproximateNumberOfMessagesDelayed",
            "ApproximateNumberOfMessagesNotVisible",
        ]

        # Our custom metrics should NOT duplicate these
        our_essential_metrics = [
            "EventsProcessedSuccessfully",  # Business metric: successful event processing
            "EventType_Login",              # Business metric: by event type
            "EventType_Logout",             # Business metric: by event type
            "EventType_ACDLogin",           # Business metric: by event type
            "EventType_ACDLogout",          # Business metric: by event type
            "EventType_AgentAvailable",     # Business metric: by event type
            "EventType_AgentBusiedOut",     # Business metric: by event type
        ]

        our_optional_metrics = [
            "PartialBatchFailures",         # Custom: SQS partial batch failures
            "EventError_duplicate_key",     # Custom: categorized errors
            "EventError_processing_error",  # Custom: categorized errors
            "CriticalProcessingErrors",     # Custom: critical errors
        ]

        # Verify no overlap
        all_aws_metrics = aws_lambda_builtin_metrics + aws_sqs_builtin_metrics
        all_our_metrics = our_essential_metrics + our_optional_metrics

        overlap = set(all_aws_metrics) & set(all_our_metrics)
        assert len(overlap) == 0, f"Our metrics should not duplicate AWS metrics: {overlap}"

        # This test passes if we're not duplicating AWS built-in metrics
        assert True

    @patch.dict('os.environ', {'ENABLE_CUSTOM_METRICS': 'false', 'ENABLE_BUSINESS_METRICS': 'true'})
    def test_environment_variable_configuration(self):
        """Test metrics configuration via environment variables."""
        # Force reload of settings to pick up env vars
        from src.agent_event_processor.config.settings import Settings
        
        settings = Settings()
        assert settings.enable_custom_metrics is False
        assert settings.enable_business_metrics is True

    def test_cost_optimization_summary(self):
        """Test that our metrics strategy is cost-optimized."""
        settings = Settings()
        
        # Default configuration should minimize costs
        assert settings.enable_custom_metrics is False, "Custom metrics are expensive and should be disabled by default"
        assert settings.enable_business_metrics is True, "Business metrics are essential and should be enabled"
        
        # Calculate estimated monthly cost savings
        # AWS CloudWatch custom metrics cost: $0.30 per metric per month
        # If we had 20 custom metrics vs 6 business metrics, we save:
        # (20 - 6) * $0.30 = $4.20 per month per Lambda function
        
        estimated_custom_metrics_avoided = 14  # Rough estimate
        monthly_savings_per_function = estimated_custom_metrics_avoided * 0.30
        
        assert monthly_savings_per_function > 4.0, "Should save significant costs by avoiding unnecessary metrics"
