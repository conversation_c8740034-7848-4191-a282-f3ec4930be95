# Agent Event Processor Lambda

A production-ready AWS Lambda function for processing agent events from SQS queues and populating Amazon Redshift dimension tables with comprehensive error handling, monitoring, and testing.

## Features

- **Event Processing**: Handles Login, Logout, AgentAvailable, AgentBusiedOut, ACDLogin, ACDLogout events
- **Star Schema Design**: Implements proper data warehouse patterns with SCD Type 2 dimensions
- **Strong Typing**: Full type safety with Pydantic models and mypy validation
- **Error Handling**: Comprehensive retry logic, DLQ routing, and failure categorization
- **Monitoring**: Structured logging, CloudWatch metrics, and operational dashboards
- **Local Development**: LocalStack integration for local testing and development

## Architecture
TBD