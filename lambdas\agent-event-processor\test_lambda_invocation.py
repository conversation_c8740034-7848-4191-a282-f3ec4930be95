#!/usr/bin/env python3
"""
Test script to validate lambda function with full invocation.

This script tests the lambda handler with various event types
to identify any issues before deployment.
"""

import sys
import json
import traceback
from unittest.mock import Mock, patch


def create_mock_context():
    """Create a mock Lambda context."""
    context = Mock()
    context.memory_limit_in_mb = 512
    context.get_remaining_time_in_millis.return_value = 300000  # 5 minutes
    context.function_name = "agent-event-processor"
    context.function_version = "1"
    context.invoked_function_arn = "arn:aws:lambda:us-east-1:123456789012:function:agent-event-processor"
    return context


def create_sqs_event(xml_content, message_id="test-msg-1"):
    """Create SQS event with XML content."""
    return {
        "Records": [
            {
                "messageId": message_id,
                "receiptHandle": f"receipt-{message_id}",
                "body": xml_content,
                "attributes": {
                    "ApproximateReceiveCount": "1",
                    "SentTimestamp": "1642248600000",
                    "SenderId": "AIDAIENQZJOLO23YVJ4VO",
                    "ApproximateFirstReceiveTimestamp": "1642248600000"
                },
                "messageAttributes": {},
                "md5OfBody": "test-md5",
                "eventSource": "aws:sqs",
                "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:agent-events-queue",
                "awsRegion": "us-east-1"
            }
        ]
    }


def test_login_event():
    """Test Login event processing."""
    print("Testing Login event...")
    
    login_xml = """<LogEvent>
        <timestamp>2024-01-15T10:30:00Z</timestamp>
        <eventType>Login</eventType>
        <agencyOrElement>Brandon911</agencyOrElement>
        <agent>john.doe</agent>
        <mediaLabel>Audio_1</mediaLabel>
        <uri>tel:+2045553006</uri>
        <agentRole>Rural - CT</agentRole>
        <workstation>OP6</workstation>
        <reason>normal</reason>
    </LogEvent>"""
    
    try:
        from src.agent_event_processor.lambda_function import lambda_handler
        
        # Mock the event processor to avoid database calls
        with patch('src.agent_event_processor.lambda_function.get_event_processor') as mock_get_processor:
            mock_processor = Mock()
            mock_get_processor.return_value = mock_processor
            
            sqs_event = create_sqs_event(login_xml)
            context = create_mock_context()
            
            response = lambda_handler(sqs_event, context)
            
            # Verify response structure
            assert response["statusCode"] == 200
            body = json.loads(response["body"])
            assert "successful_count" in body
            assert "failed_count" in body
            assert body["successful_count"] == 1
            assert body["failed_count"] == 0
            
            # Verify processor was called
            assert mock_processor.process_single_event.called
            
            print("✓ Login event test passed")
            return True
            
    except Exception as e:
        print(f"✗ Login event test failed: {e}")
        traceback.print_exc()
        return False


def test_acd_login_event():
    """Test ACD Login event processing."""
    print("Testing ACD Login event...")
    
    acd_login_xml = """<LogEvent>
        <timestamp>2024-01-15T10:32:00Z</timestamp>
        <eventType>ACDLogin</eventType>
        <agencyOrElement>Brandon911</agencyOrElement>
        <agent>john.doe</agent>
        <ringGroupName>911 Queue</ringGroupName>
        <ringGroupUri>sip:<EMAIL></ringGroupUri>
    </LogEvent>"""
    
    try:
        from src.agent_event_processor.lambda_function import lambda_handler
        
        with patch('src.agent_event_processor.lambda_function.get_event_processor') as mock_get_processor:
            mock_processor = Mock()
            mock_get_processor.return_value = mock_processor
            
            sqs_event = create_sqs_event(acd_login_xml)
            context = create_mock_context()
            
            response = lambda_handler(sqs_event, context)
            
            assert response["statusCode"] == 200
            body = json.loads(response["body"])
            assert body["successful_count"] == 1
            assert body["failed_count"] == 0
            
            print("✓ ACD Login event test passed")
            return True
            
    except Exception as e:
        print(f"✗ ACD Login event test failed: {e}")
        traceback.print_exc()
        return False


def test_batch_processing():
    """Test batch processing with multiple events."""
    print("Testing batch processing...")
    
    events = [
        """<LogEvent>
            <timestamp>2024-01-15T10:30:00Z</timestamp>
            <eventType>Login</eventType>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>john.doe</agent>
        </LogEvent>""",
        """<LogEvent>
            <timestamp>2024-01-15T10:31:00Z</timestamp>
            <eventType>AgentAvailable</eventType>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>john.doe</agent>
        </LogEvent>""",
        """<LogEvent>
            <timestamp>2024-01-15T10:32:00Z</timestamp>
            <eventType>Logout</eventType>
            <agencyOrElement>Brandon911</agencyOrElement>
            <agent>john.doe</agent>
        </LogEvent>"""
    ]
    
    try:
        from src.agent_event_processor.lambda_function import lambda_handler
        
        with patch('src.agent_event_processor.lambda_function.get_event_processor') as mock_get_processor:
            mock_processor = Mock()
            mock_get_processor.return_value = mock_processor
            
            # Create batch event
            sqs_event = {
                "Records": [
                    {
                        "messageId": f"msg-{i}",
                        "receiptHandle": f"receipt-{i}",
                        "body": xml_content,
                        "attributes": {},
                        "messageAttributes": {},
                        "md5OfBody": "test-md5",
                        "eventSource": "aws:sqs",
                        "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:agent-events-queue",
                        "awsRegion": "us-east-1"
                    }
                    for i, xml_content in enumerate(events)
                ]
            }
            
            context = create_mock_context()
            response = lambda_handler(sqs_event, context)
            
            assert response["statusCode"] == 200
            body = json.loads(response["body"])
            assert body["successful_count"] == 3
            assert body["failed_count"] == 0
            
            # Verify all events were processed
            assert mock_processor.process_single_event.call_count == 3
            
            print("✓ Batch processing test passed")
            return True
            
    except Exception as e:
        print(f"✗ Batch processing test failed: {e}")
        traceback.print_exc()
        return False


def test_invalid_xml_handling():
    """Test handling of invalid XML."""
    print("Testing invalid XML handling...")
    
    try:
        from src.agent_event_processor.lambda_function import lambda_handler
        
        with patch('src.agent_event_processor.lambda_function.get_event_processor') as mock_get_processor:
            mock_processor = Mock()
            mock_get_processor.return_value = mock_processor
            
            # Create event with invalid XML
            sqs_event = create_sqs_event("This is not valid XML")
            context = create_mock_context()
            
            response = lambda_handler(sqs_event, context)
            
            assert response["statusCode"] == 200
            body = json.loads(response["body"])
            assert body["successful_count"] == 0
            assert body["failed_count"] == 1
            
            # Should have batch item failures
            assert "batchItemFailures" in response
            assert len(response["batchItemFailures"]) == 1
            
            print("✓ Invalid XML handling test passed")
            return True
            
    except Exception as e:
        print(f"✗ Invalid XML handling test failed: {e}")
        traceback.print_exc()
        return False


def test_metrics_configuration():
    """Test metrics configuration."""
    print("Testing metrics configuration...")
    
    try:
        from src.agent_event_processor.config.settings import Settings
        
        # Test default settings
        settings = Settings()
        assert settings.enable_custom_metrics is False, "Custom metrics should be disabled by default"
        assert settings.enable_business_metrics is True, "Business metrics should be enabled by default"
        
        # Test custom configuration
        settings = Settings(enable_custom_metrics=True, enable_business_metrics=False)
        assert settings.enable_custom_metrics is True
        assert settings.enable_business_metrics is False
        
        print("✓ Metrics configuration test passed")
        return True
        
    except Exception as e:
        print(f"✗ Metrics configuration test failed: {e}")
        traceback.print_exc()
        return False


def test_xml_parser():
    """Test XML parser functionality."""
    print("Testing XML parser...")
    
    try:
        from src.agent_event_processor.utils.xml_parser import AgentEventXMLParser
        
        valid_xml = """<LogEvent>
            <timestamp>2024-01-15T10:30:00Z</timestamp>
            <eventType>Login</eventType>
            <agencyOrElement>Test911</agencyOrElement>
            <agent>test.user</agent>
        </LogEvent>"""
        
        result = AgentEventXMLParser.xml_to_json(valid_xml)
        assert result["eventType"] == "Login"
        assert result["agent"] == "test.user"
        
        print("✓ XML parser test passed")
        return True
        
    except Exception as e:
        print(f"✗ XML parser test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("TESTING LAMBDA FUNCTION WITH FULL INVOCATION")
    print("=" * 60)
    
    tests = [
        test_xml_parser,
        test_metrics_configuration,
        test_login_event,
        test_acd_login_event,
        test_batch_processing,
        test_invalid_xml_handling,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            failed += 1
        print()  # Add spacing between tests
    
    print("=" * 60)
    print(f"TEST RESULTS: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("🎉 All tests passed! Lambda function is ready for deployment.")
        return 0
    else:
        print("❌ Some tests failed. Please review the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
