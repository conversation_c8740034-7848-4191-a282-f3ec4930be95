"""Integration tests for Lambda function."""

import json
import pytest
import requests
from datetime import datetime
from typing import Dict, Any


class TestLambdaIntegration:
    """Integration tests for the Lambda function."""

    @pytest.fixture
    def lambda_url(self):
        """Lambda function URL for testing."""
        return "http://localhost:9000/2015-03-31/functions/function/invocations"

    @pytest.fixture
    def sample_login_xml(self):
        """Sample login event XML."""
        return """<LogEvent xmlns="http://solacom.com/Logging">
<timestamp>2025-02-05T11:44:18.031Z</timestamp>
<agencyOrElement>Brandon911</agencyOrElement>
<agent>test-agent-001</agent>
<eventType>Login</eventType>
<login>
<mediaLabel>_ML_194D5ECDE50C0001C46A@BrandonMB</mediaLabel>
<uri>tel:+2045553006</uri>
<agentRole>Rural - CT</agentRole>
<reason>normal</reason>
<operatorId>OP001</operatorId>
<workstation>WS-001</workstation>
<deviceName>Headset</deviceName>
</login>
</LogEvent>"""

    @pytest.fixture
    def sample_busied_out_xml(self):
        """Sample busied out event XML."""
        return """<LogEvent xmlns="http://solacom.com/Logging">
<timestamp>2025-02-05T11:45:18.031Z</timestamp>
<agencyOrElement>Brandon911</agencyOrElement>
<agent>test-agent-001</agent>
<eventType>AgentBusiedOut</eventType>
<agentbusiedout>
<reason>break</reason>
<action>manual</action>
<duration>900</duration>
</agentbusiedout>
</LogEvent>"""

    def create_sqs_event(self, xml_message: str, message_id: str = "test-message") -> Dict[str, Any]:
        """Create SQS event structure."""
        return {
            "Records": [
                {
                    "messageId": message_id,
                    "receiptHandle": f"test-receipt-{message_id}",
                    "body": json.dumps({"Message": xml_message}),
                    "attributes": {
                        "ApproximateReceiveCount": "1",
                        "SentTimestamp": "1640995200000",
                        "SenderId": "AIDAIENQZJOLO23YVJ4VO",
                        "ApproximateFirstReceiveTimestamp": "1640995200000"
                    },
                    "messageAttributes": {},
                    "md5OfBody": f"test-md5-{message_id}",
                    "eventSource": "aws:sqs",
                    "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:test-queue",
                    "awsRegion": "us-east-1"
                }
            ]
        }

    def test_login_event_processing(self, lambda_url, sample_login_xml):
        """Test processing of login event."""
        # Arrange
        sqs_event = self.create_sqs_event(sample_login_xml, "login-test")

        # Act
        response = requests.post(lambda_url, json=sqs_event, timeout=30)

        # Assert
        assert response.status_code == 200
        response_data = json.loads(response.text)
        assert response_data.get('statusCode') == 200
        
        body = json.loads(response_data.get('body', '{}'))
        assert body.get('successful_count', 0) >= 1
        assert body.get('failed_count', 0) == 0

    def test_busied_out_event_processing(self, lambda_url, sample_busied_out_xml):
        """Test processing of busied out event."""
        # Arrange
        sqs_event = self.create_sqs_event(sample_busied_out_xml, "busied-out-test")

        # Act
        response = requests.post(lambda_url, json=sqs_event, timeout=30)

        # Assert
        assert response.status_code == 200
        response_data = json.loads(response.text)
        assert response_data.get('statusCode') == 200

    def test_batch_event_processing(self, lambda_url, sample_login_xml, sample_busied_out_xml):
        """Test processing of multiple events in batch."""
        # Arrange
        batch_event = {
            "Records": [
                {
                    "messageId": "batch-login",
                    "receiptHandle": "test-receipt-batch-login",
                    "body": json.dumps({"Message": sample_login_xml}),
                    "attributes": {
                        "ApproximateReceiveCount": "1",
                        "SentTimestamp": "1640995200000",
                        "SenderId": "AIDAIENQZJOLO23YVJ4VO",
                        "ApproximateFirstReceiveTimestamp": "1640995200000"
                    },
                    "messageAttributes": {},
                    "md5OfBody": "test-md5-batch-login",
                    "eventSource": "aws:sqs",
                    "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:test-queue",
                    "awsRegion": "us-east-1"
                },
                {
                    "messageId": "batch-busied-out",
                    "receiptHandle": "test-receipt-batch-busied-out",
                    "body": json.dumps({"Message": sample_busied_out_xml}),
                    "attributes": {
                        "ApproximateReceiveCount": "1",
                        "SentTimestamp": "1640995201000",
                        "SenderId": "AIDAIENQZJOLO23YVJ4VO",
                        "ApproximateFirstReceiveTimestamp": "1640995201000"
                    },
                    "messageAttributes": {},
                    "md5OfBody": "test-md5-batch-busied-out",
                    "eventSource": "aws:sqs",
                    "eventSourceARN": "arn:aws:sqs:us-east-1:123456789012:test-queue",
                    "awsRegion": "us-east-1"
                }
            ]
        }

        # Act
        response = requests.post(lambda_url, json=batch_event, timeout=30)

        # Assert
        assert response.status_code == 200
        response_data = json.loads(response.text)
        assert response_data.get('statusCode') == 200

    def test_invalid_xml_handling(self, lambda_url):
        """Test handling of invalid XML."""
        # Arrange
        invalid_xml = "<invalid>xml without proper structure"
        sqs_event = self.create_sqs_event(invalid_xml, "invalid-xml-test")

        # Act
        response = requests.post(lambda_url, json=sqs_event, timeout=30)

        # Assert
        assert response.status_code == 200
        response_data = json.loads(response.text)
        
        # Should have batch item failures for invalid XML
        batch_failures = response_data.get('batchItemFailures', [])
        assert len(batch_failures) > 0

    def test_missing_required_fields(self, lambda_url):
        """Test handling of XML with missing required fields."""
        # Arrange
        incomplete_xml = """<LogEvent xmlns="http://solacom.com/Logging">
<timestamp>2025-02-05T11:44:18.031Z</timestamp>
<agencyOrElement>Brandon911</agencyOrElement>
</LogEvent>"""
        sqs_event = self.create_sqs_event(incomplete_xml, "incomplete-test")

        # Act
        response = requests.post(lambda_url, json=sqs_event, timeout=30)

        # Assert
        assert response.status_code == 200
        response_data = json.loads(response.text)
        
        # Should handle gracefully with batch failures
        batch_failures = response_data.get('batchItemFailures', [])
        assert len(batch_failures) > 0

    @pytest.mark.skip(reason="Requires database connection")
    def test_database_connectivity(self, lambda_url, sample_login_xml):
        """Test database connectivity and data persistence."""
        # This test would require actual database verification
        # Skip by default as it requires database setup
        pass

    def test_lambda_response_format(self, lambda_url, sample_login_xml):
        """Test Lambda response format compliance."""
        # Arrange
        sqs_event = self.create_sqs_event(sample_login_xml, "format-test")

        # Act
        response = requests.post(lambda_url, json=sqs_event, timeout=30)

        # Assert
        assert response.status_code == 200
        response_data = json.loads(response.text)
        
        # Verify required response fields
        assert 'statusCode' in response_data
        assert 'body' in response_data
        
        body = json.loads(response_data['body'])
        assert 'message' in body
        assert 'successful_count' in body
        assert 'failed_count' in body

    def test_lambda_timeout_handling(self, lambda_url):
        """Test Lambda timeout handling."""
        # Arrange - Use a very short timeout to test timeout handling
        sqs_event = self.create_sqs_event("<test>timeout</test>", "timeout-test")

        # Act
        try:
            response = requests.post(lambda_url, json=sqs_event, timeout=1)  # Very short timeout
            # If we get here, the Lambda responded quickly
            assert response.status_code == 200
        except requests.exceptions.Timeout:
            # This is expected for timeout testing
            pytest.skip("Timeout test - Lambda took longer than expected")
