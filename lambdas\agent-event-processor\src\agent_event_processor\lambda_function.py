"""
Main Lambda handler for processing agent events from SQS.

This module provides the entry point for the AWS Lambda function that processes
agent events from SQS queues and populates Redshift dimension tables.
"""

import json
from typing import Any, Dict, Optional

from aws_lambda_powertools import Logger, Tracer, Metrics
from aws_lambda_powertools.metrics import MetricUnit
from aws_lambda_typing import context as LambdaContext, events

from .config import get_settings
from .models.events import AgentEvent
from .services.event_processor import EventProcessor
from .utils.xml_parser import AgentEventXMLParser

# Initialize Lambda Powertools
logger = Logger()
tracer = Tracer()
metrics = Metrics()

# Global variables for Lambda container reuse
_event_processor: Optional[EventProcessor] = None


def _prepare_event_data(json_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare hierarchical JSON data for the AgentEvent model.

    Args:
        json_data: Hierarchical JSON data from XML conversion

    Returns:
        Data structured for AgentEvent with event_data sub-dictionary
    """
    # Extract top-level fields
    event_fields = {}
    event_data = {}

    # Copy top-level fields (skip XML namespace attributes)
    for key, value in json_data.items():
        if not isinstance(value, dict) and not key.startswith('@'):
            event_fields[key] = value

    # Extract event-specific data based on event type
    event_type = json_data.get("eventType")
    if event_type:
        # Map event types to their data keys
        event_data_keys = {
            "Login": "login",
            "Logout": "logout",
            "AgentAvailable": "agentAvailable",
            "AgentBusiedOut": "busiedOut",
            "ACDLogin": "acdLogin",
            "ACDLogout": "acdLogout"
        }

        data_key = event_data_keys.get(event_type)
        if data_key and data_key in json_data:
            event_data = json_data[data_key]
            if not isinstance(event_data, dict):
                event_data = {}

    # Combine top-level fields with event_data
    result = event_fields.copy()
    result["event_data"] = event_data

    return result


def get_event_processor() -> EventProcessor:
    """
    Get or create event processor instance (singleton for container reuse).

    Returns:
        EventProcessor: Configured event processor instance.
    """
    global _event_processor

    if _event_processor is None:
        settings = get_settings()
        _event_processor = EventProcessor(settings)
        logger.info("Event processor initialized")

    return _event_processor


@logger.inject_lambda_context
@tracer.capture_lambda_handler
@metrics.log_metrics
def lambda_handler(event: events.SQSEvent, context: LambdaContext) -> Dict[str, Any]:
    """
    Main Lambda handler for processing agent events from SQS.

    This function processes batches of agent events from SQS, validates them,
    and populates the appropriate Redshift dimension and fact tables.

    Args:
        event: SQS event containing Records array with agent events.
        context: Lambda execution context with request metadata.

    Returns:
        Dict containing processing results and any failed message identifiers
        for partial batch failure handling.

    Raises:
        Exception: Re-raises any unhandled exceptions after logging.
    """

    try:
        # Extract SQS records
        records = event.get("Records", [])
        logger.info(
            "Processing SQS batch",
            record_count=len(records),
            memory_limit_mb=context.memory_limit_in_mb,
        )

        # Process each record
        successful_count = 0
        failed_count = 0
        failed_message_ids = []

        for record in records:
            try:
                # Extract and convert XML to JSON
                xml_content = AgentEventXMLParser.extract_xml_from_sqs_message(record["body"])
                json_data = AgentEventXMLParser.xml_to_json(xml_content)

                # Prepare hierarchical data for AgentEvent model
                event_data = _prepare_event_data(json_data)

                # Validate with Pydantic model
                agent_event = AgentEvent.model_validate(event_data)

                # Add SQS metadata
                agent_event.sqs_message_id = record.get("messageId")
                agent_event.sqs_receipt_handle = record.get("receiptHandle")

                # Process the event (store to database)
                processor = get_event_processor()
                processor.process_single_event(agent_event, context)

                successful_count += 1
                logger.info(
                    "Successfully processed event",
                    event_type=agent_event.event_type,
                    agent=agent_event.agent,
                    message_id=record.get("messageId")
                )

            except Exception as e:
                failed_count += 1
                message_id = record.get("messageId", "unknown")
                failed_message_ids.append(message_id)

                logger.error(
                    "Failed to process record",
                    error=str(e),
                    message_id=message_id,
                    record_body=record.get("body", "")[:500],
                    exc_info=True
                )

        # Add metrics
        metrics.add_metric(name="ProcessedRecords", unit=MetricUnit.Count, value=len(records))
        metrics.add_metric(name="SuccessfulRecords", unit=MetricUnit.Count, value=successful_count)
        metrics.add_metric(name="FailedRecords", unit=MetricUnit.Count, value=failed_count)

        if failed_count > 0:
            metrics.add_metric(name="BatchPartialFailure", unit=MetricUnit.Count, value=1)
            logger.warning(
                "Batch processing completed with failures",
                successful_count=successful_count,
                failed_count=failed_count,
                failed_message_ids=failed_message_ids,
            )
        else:
            metrics.add_metric(name="BatchSuccess", unit=MetricUnit.Count, value=1)
            logger.info(
                "Batch processing completed successfully",
                successful_count=successful_count
            )

        # Create batch item failures for SQS partial batch failure handling
        batch_item_failures = []
        for message_id in failed_message_ids:
            batch_item_failures.append({"itemIdentifier": message_id})

        # Return response with partial batch failure support
        response = {
            "statusCode": 200,
            "body": json.dumps({
                "message": "Batch processing completed",
                "successful_count": successful_count,
                "failed_count": failed_count,
            }),
        }

        # Include batch item failures for SQS partial batch failure
        if batch_item_failures:
            response["batchItemFailures"] = batch_item_failures

        return response

    except Exception as e:
        # Log the error with full context
        logger.error(
            "Lambda execution failed",
            extra={
                "error": str(e),
                "error_type": type(e).__name__,
            },
            exc_info=True,
        )

        # Publish error metric using Lambda Powertools
        metrics.add_metric(name="LambdaErrors", unit=MetricUnit.Count, value=1)

        # Re-raise to trigger Lambda error handling
        raise
