"""
CloudWatch metrics collection and publishing for Agent Event Processor.

This module provides structured metrics collection with batch publishing
to CloudWatch, optimized for Lambda execution patterns.
"""

import boto3
from datetime import datetime
from typing import Dict, List, Optional
from aws_lambda_powertools import Logger

logger = Logger()


class MetricsCollector:
    """
    Collects and publishes CloudWatch metrics with batch optimization.

    This class buffers metrics and publishes them in batches to reduce
    CloudWatch API calls and improve Lambda performance.
    """

    def __init__(
        self,
        namespace: str = "SmartAnalytics/AgentEventProcessor",
        enabled: bool = True,
    ):
        """
        Initialize metrics collector.

        Args:
            namespace: CloudWatch metrics namespace.
            enabled: Whether metrics publishing is enabled.
        """
        self.namespace = namespace
        self.enabled = enabled
        self.metrics_buffer: List[Dict] = []

        if self.enabled:
            self.cloudwatch = boto3.client("cloudwatch")
            logger.info("Metrics collector initialized", namespace=namespace)
        else:
            logger.info("Metrics collection disabled")

    def put_metric(
        self,
        metric_name: str,
        value: float,
        unit: str = "Count",
        dimensions: Optional[Dict[str, str]] = None,
    ) -> None:
        """
        Add metric to buffer for batch publishing.

        Args:
            metric_name: Name of the metric.
            value: Metric value.
            unit: CloudWatch unit (Count, Seconds, Bytes, etc.).
            dimensions: Optional metric dimensions for filtering.
        """
        if not self.enabled:
            return

        metric_data = {
            "MetricName": metric_name,
            "Value": value,
            "Unit": unit,
            "Timestamp": datetime.utcnow(),
        }

        if dimensions:
            metric_data["Dimensions"] = [
                {"Name": k, "Value": v} for k, v in dimensions.items()
            ]

        self.metrics_buffer.append(metric_data)

        logger.debug(
            "Metric added to buffer",
            metric_name=metric_name,
            value=value,
            unit=unit,
            dimensions=dimensions,
            buffer_size=len(self.metrics_buffer),
        )

        # Auto-flush if buffer approaches CloudWatch limit (20 metrics per call)
        if len(self.metrics_buffer) >= 20:
            self.flush_metrics()

    def put_business_metric(self, event_type: str, tenant: str, count: int = 1) -> None:
        """
        Publish business-specific metrics with standard dimensions.

        Args:
            event_type: Type of agent event processed.
            tenant: Tenant identifier.
            count: Number of events processed.
        """
        dimensions = {"EventType": event_type, "Tenant": tenant}

        self.put_metric(
            metric_name="EventsProcessed",
            value=count,
            unit="Count",
            dimensions=dimensions,
        )

    def put_performance_metric(
        self, operation: str, duration_ms: float, success: bool = True
    ) -> None:
        """
        Publish performance metrics for operations.

        Args:
            operation: Name of the operation being measured.
            duration_ms: Operation duration in milliseconds.
            success: Whether the operation was successful.
        """
        dimensions = {
            "Operation": operation,
            "Status": "Success" if success else "Error",
        }

        self.put_metric(
            metric_name="OperationDuration",
            value=duration_ms,
            unit="Milliseconds",
            dimensions=dimensions,
        )

        self.put_metric(
            metric_name="OperationCount", value=1, unit="Count", dimensions=dimensions
        )

    def put_database_metric(
        self, operation: str, table_name: str, row_count: int, duration_ms: float
    ) -> None:
        """
        Publish database operation metrics.

        Args:
            operation: Database operation (INSERT, UPDATE, SELECT).
            table_name: Target table name.
            row_count: Number of rows affected.
            duration_ms: Operation duration in milliseconds.
        """
        dimensions = {"Operation": operation, "Table": table_name}

        self.put_metric(
            metric_name="DatabaseOperationDuration",
            value=duration_ms,
            unit="Milliseconds",
            dimensions=dimensions,
        )

        self.put_metric(
            metric_name="DatabaseRowsAffected",
            value=row_count,
            unit="Count",
            dimensions=dimensions,
        )

    def flush_metrics(self) -> None:
        """
        Publish all buffered metrics to CloudWatch.

        Publishes metrics in batches and clears the buffer. Handles errors
        gracefully to avoid impacting main processing flow.
        """
        if not self.enabled or not self.metrics_buffer:
            return

        try:
            # CloudWatch allows max 20 metrics per call
            batch_size = 20
            total_metrics = len(self.metrics_buffer)

            for i in range(0, total_metrics, batch_size):
                batch = self.metrics_buffer[i : i + batch_size]

                self.cloudwatch.put_metric_data(
                    Namespace=self.namespace, MetricData=batch
                )

                logger.debug(
                    "Metrics batch published",
                    batch_size=len(batch),
                    namespace=self.namespace,
                )

            logger.info(
                "All metrics published to CloudWatch",
                total_metrics=total_metrics,
                namespace=self.namespace,
            )

            # Clear the buffer after successful publishing
            self.metrics_buffer.clear()

        except Exception as e:
            logger.error(
                "Failed to publish metrics to CloudWatch",
                error=str(e),
                metric_count=len(self.metrics_buffer),
                namespace=self.namespace,
                exc_info=True,
            )
            # Don't clear buffer on error - metrics will be retried on next flush

    def __del__(self) -> None:
        """Ensure metrics are flushed when collector is destroyed."""
        if hasattr(self, "metrics_buffer") and self.metrics_buffer:
            self.flush_metrics()
