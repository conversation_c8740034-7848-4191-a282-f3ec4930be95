#!/usr/bin/env python3
"""
Populate dimension tables for testing.

This script populates the dim_date and dim_time tables with data
to support fact table inserts during testing.
"""

import os
import sys
import psycopg2
from datetime import datetime, timedelta
from typing import List, Tuple

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

def get_db_connection():
    """Get database connection for local PostgreSQL."""
    return psycopg2.connect(
        host=os.getenv('DB_HOST', 'localhost'),
        port=int(os.getenv('DB_PORT', 5432)),
        database=os.getenv('DB_NAME', 'agent_events'),
        user=os.getenv('DB_USER', 'postgres'),
        password=os.getenv('DB_PASSWORD', 'password')
    )

def populate_date_dimension(conn) -> None:
    """Populate dim_date table with date range."""
    print("Populating dim_date table...")
    
    with conn.cursor() as cursor:
        # Generate dates for the past year and next year
        start_date = datetime.now() - timedelta(days=365)
        end_date = datetime.now() + timedelta(days=365)
        
        current_date = start_date
        batch_size = 100
        dates_batch = []
        
        while current_date <= end_date:
            date_key = int(current_date.strftime("%Y%m%d"))
            
            dates_batch.append((
                date_key,
                current_date.date(),
                current_date.year,
                current_date.month,
                current_date.day,
                current_date.weekday() + 1,  # Monday = 1
                current_date.strftime("%A"),
                current_date.strftime("%B"),
                current_date.quarter if hasattr(current_date, 'quarter') else ((current_date.month - 1) // 3) + 1
            ))
            
            if len(dates_batch) >= batch_size:
                insert_date_batch(cursor, dates_batch)
                dates_batch = []
            
            current_date += timedelta(days=1)
        
        # Insert remaining dates
        if dates_batch:
            insert_date_batch(cursor, dates_batch)
        
        conn.commit()
        print(f"Populated dim_date with dates from {start_date.date()} to {end_date.date()}")

def insert_date_batch(cursor, dates_batch: List[Tuple]) -> None:
    """Insert a batch of dates."""
    insert_sql = """
        INSERT INTO dim_date (
            date_key, date_value, year, month, day, 
            day_of_week, day_name, month_name, quarter
        ) VALUES %s
        ON CONFLICT (date_key) DO NOTHING
    """
    
    from psycopg2.extras import execute_values
    execute_values(cursor, insert_sql, dates_batch)

def populate_time_dimension(conn) -> None:
    """Populate dim_time table with time values."""
    print("Populating dim_time table...")
    
    with conn.cursor() as cursor:
        batch_size = 100
        times_batch = []
        
        # Generate all times for 24 hours (every minute)
        for hour in range(24):
            for minute in range(60):
                for second in [0, 30]:  # Every 30 seconds to reduce volume
                    time_key = hour * 10000 + minute * 100 + second
                    time_value = f"{hour:02d}:{minute:02d}:{second:02d}"
                    
                    times_batch.append((
                        time_key,
                        time_value,
                        hour,
                        minute,
                        second,
                        "AM" if hour < 12 else "PM"
                    ))
                    
                    if len(times_batch) >= batch_size:
                        insert_time_batch(cursor, times_batch)
                        times_batch = []
        
        # Insert remaining times
        if times_batch:
            insert_time_batch(cursor, times_batch)
        
        conn.commit()
        print("Populated dim_time with time values")

def insert_time_batch(cursor, times_batch: List[Tuple]) -> None:
    """Insert a batch of times."""
    insert_sql = """
        INSERT INTO dim_time (
            time_key, time_value, hour, minute, second, am_pm
        ) VALUES %s
        ON CONFLICT (time_key) DO NOTHING
    """
    
    from psycopg2.extras import execute_values
    execute_values(cursor, insert_sql, times_batch)

def create_dimension_tables_if_not_exist(conn) -> None:
    """Create dimension tables if they don't exist."""
    print("Creating dimension tables if they don't exist...")
    
    with conn.cursor() as cursor:
        # Create dim_date table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS dim_date (
                date_key INTEGER PRIMARY KEY,
                date_value DATE NOT NULL,
                year INTEGER NOT NULL,
                month INTEGER NOT NULL,
                day INTEGER NOT NULL,
                day_of_week INTEGER NOT NULL,
                day_name VARCHAR(10) NOT NULL,
                month_name VARCHAR(10) NOT NULL,
                quarter INTEGER NOT NULL
            )
        """)
        
        # Create dim_time table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS dim_time (
                time_key INTEGER PRIMARY KEY,
                time_value TIME NOT NULL,
                hour INTEGER NOT NULL,
                minute INTEGER NOT NULL,
                second INTEGER NOT NULL,
                am_pm VARCHAR(2) NOT NULL
            )
        """)
        
        conn.commit()
        print("Dimension tables created/verified")

def main():
    """Main function to populate dimension tables."""
    try:
        print("Starting dimension table population...")
        
        # Connect to database
        conn = get_db_connection()
        print("Connected to database")
        
        # Create tables if they don't exist
        create_dimension_tables_if_not_exist(conn)
        
        # Populate dimension tables
        populate_date_dimension(conn)
        populate_time_dimension(conn)
        
        # Verify data
        with conn.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM dim_date")
            date_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM dim_time")
            time_count = cursor.fetchone()[0]
            
            print(f"\nDimension tables populated successfully:")
            print(f"  dim_date: {date_count:,} records")
            print(f"  dim_time: {time_count:,} records")
        
        conn.close()
        print("\nDimension population completed successfully!")
        
    except Exception as e:
        print(f"Error populating dimensions: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
