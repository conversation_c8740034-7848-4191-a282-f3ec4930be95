-- Initialize test database schema for local testing
-- This script creates the necessary tables for agent event processing

-- Create dimension tables
CREATE TABLE IF NOT EXISTS dim_tenant (
    tenant_key BIGSERIAL PRIMARY KEY,
    tenant_name VARCHAR(100) NOT NULL UNIQUE,
    timezone_name VA<PERSON>HA<PERSON>(50) DEFAULT 'UTC',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS dim_agent (
    agent_key BIGSERIAL PRIMARY KEY,
    agent_name VARCHAR(100) NOT NULL,
    operator_id VARCHAR(50),
    agent_role VARCHAR(100),
    agent_uri VARCHAR(200),
    workstation VARCHAR(200),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    valid_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    valid_to TIMESTAMP,
    is_current BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS dim_queue (
    queue_key BIGSERIAL PRIMARY KEY,
    ring_group_name VARCHAR(200) NOT NULL,
    ring_group_uri VARCHAR(500),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    valid_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    valid_to TIMESTAMP,
    is_current BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS dim_date (
    date_key INTEGER PRIMARY KEY,
    full_date DATE,
    year INTEGER,
    quarter INTEGER,
    month INTEGER,
    day INTEGER,
    day_of_week INTEGER,
    day_name VARCHAR(10),
    month_name VARCHAR(10),
    is_weekend BOOLEAN
);

CREATE TABLE IF NOT EXISTS dim_time (
    time_key INTEGER PRIMARY KEY,
    hour INTEGER,
    minute INTEGER,
    second INTEGER,
    time_of_day VARCHAR(8),
    hour_name VARCHAR(10)
);

-- Create fact tables
CREATE TABLE IF NOT EXISTS fact_agent_event (
    state_key BIGSERIAL PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agent(agent_key),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    date_key INTEGER REFERENCES dim_date(date_key),
    time_key INTEGER REFERENCES dim_time(time_key),
    queue_key BIGINT REFERENCES dim_queue(queue_key),
    event_timestamp_utc TIMESTAMP NOT NULL,
    event_timestamp_local TIMESTAMP,
    event_type VARCHAR(50) NOT NULL,
    reason_code VARCHAR(100),
    busied_out_action VARCHAR(100),
    busied_out_duration INTEGER,
    media_label VARCHAR(200),
    workstation VARCHAR(200),
    device_name VARCHAR(50),
    processed_at_utc TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS fact_agent_intervals (
    interval_key BIGSERIAL PRIMARY KEY,
    agent_key BIGINT REFERENCES dim_agent(agent_key),
    tenant_key BIGINT REFERENCES dim_tenant(tenant_key),
    date_key INTEGER REFERENCES dim_date(date_key),
    start_time_key INTEGER REFERENCES dim_time(time_key),
    end_time_key INTEGER REFERENCES dim_time(time_key),
    shift_date_key INTEGER REFERENCES dim_date(date_key),
    interval_start_utc TIMESTAMP NOT NULL,
    interval_end_utc TIMESTAMP,
    interval_start_local TIMESTAMP,
    interval_end_local TIMESTAMP,
    interval_type VARCHAR(50) NOT NULL,
    duration_seconds INTEGER,
    reason_code VARCHAR(100),
    is_current_interval BOOLEAN DEFAULT FALSE,
    processed_at_utc TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS fact_acd_session (
    session_key SERIAL PRIMARY KEY,
    agent_key INTEGER REFERENCES dim_agent(agent_key),
    queue_key INTEGER REFERENCES dim_queue(queue_key),
    tenant_key INTEGER REFERENCES dim_tenant(tenant_key),
    date_key INTEGER REFERENCES dim_date(date_key),
    login_time_key INTEGER REFERENCES dim_time(time_key),
    logout_time_key INTEGER REFERENCES dim_time(time_key),
    acd_login_utc TIMESTAMP NOT NULL,
    acd_logout_utc TIMESTAMP,
    acd_login_local TIMESTAMP,
    acd_logout_local TIMESTAMP,
    session_duration_seconds INTEGER,
    is_active_session BOOLEAN DEFAULT FALSE,
    processed_at_utc TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_dim_agent_tenant_name ON dim_agent(tenant_key, agent_name, is_current);
CREATE INDEX IF NOT EXISTS idx_dim_queue_tenant_name ON dim_queue(tenant_key, ring_group_name, is_current);
CREATE INDEX IF NOT EXISTS idx_fact_agent_event_timestamp ON fact_agent_event(event_timestamp_utc);
CREATE INDEX IF NOT EXISTS idx_fact_agent_intervals_current ON fact_agent_intervals(agent_key, is_current_interval);
CREATE INDEX IF NOT EXISTS idx_fact_acd_session_active ON fact_acd_session(agent_key, queue_key, is_active_session);

-- Insert test data
INSERT INTO dim_tenant (tenant_name, timezone_name) VALUES 
    ('Brandon911', 'America/Winnipeg'),
    ('TestTenant', 'UTC')
ON CONFLICT (tenant_name) DO NOTHING;

-- Populate date dimension with sample dates
INSERT INTO dim_date (date_key, full_date, year, quarter, month, day, day_of_week, day_name, month_name, is_weekend)
SELECT 
    CAST(TO_CHAR(date_series, 'YYYYMMDD') AS INTEGER) as date_key,
    date_series::date as full_date,
    EXTRACT(YEAR FROM date_series) as year,
    EXTRACT(QUARTER FROM date_series) as quarter,
    EXTRACT(MONTH FROM date_series) as month,
    EXTRACT(DAY FROM date_series) as day,
    EXTRACT(DOW FROM date_series) as day_of_week,
    TO_CHAR(date_series, 'Day') as day_name,
    TO_CHAR(date_series, 'Month') as month_name,
    CASE WHEN EXTRACT(DOW FROM date_series) IN (0, 6) THEN TRUE ELSE FALSE END as is_weekend
FROM generate_series('2024-01-01'::date, '2024-12-31'::date, '1 day'::interval) as date_series
ON CONFLICT (date_key) DO NOTHING;

-- Populate time dimension with sample times (every hour)
INSERT INTO dim_time (time_key, hour, minute, second, time_of_day, hour_name)
SELECT 
    hour * 10000 as time_key,
    hour,
    0 as minute,
    0 as second,
    LPAD(hour::text, 2, '0') || ':00:00' as time_of_day,
    CASE 
        WHEN hour = 0 THEN 'Midnight'
        WHEN hour = 12 THEN 'Noon'
        WHEN hour < 12 THEN hour::text || ' AM'
        ELSE (hour - 12)::text || ' PM'
    END as hour_name
FROM generate_series(0, 23) as hour
ON CONFLICT (time_key) DO NOTHING;
