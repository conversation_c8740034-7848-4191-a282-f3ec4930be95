["tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_acd_login_event_full_invocation", "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_all_event_types_batch", "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_empty_records", "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_login_event_full_invocation", "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_metrics_configuration", "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_partial_batch_failure", "tests/unit/test_lambda_full_invocation.py::TestLambdaFullInvocation::test_sns_wrapped_message", "tests/unit/test_models.py::TestAgentEvent::test_acd_event_validation", "tests/unit/test_models.py::TestAgentEvent::test_extra_fields_rejected", "tests/unit/test_models.py::TestAgentEvent::test_invalid_agent_uri", "tests/unit/test_models.py::TestAgentEvent::test_missing_required_fields", "tests/unit/test_models.py::TestAgentEvent::test_timestamp_parsing", "tests/unit/test_models.py::TestAgentEvent::test_valid_acd_login_event", "tests/unit/test_models.py::TestAgentEvent::test_valid_login_event", "tests/unit/test_models.py::TestDatabaseCredentials::test_empty_host", "tests/unit/test_models.py::TestDatabaseCredentials::test_invalid_port", "tests/unit/test_models.py::TestDatabaseCredentials::test_valid_credentials", "tests/unit/test_models.py::TestDimensionKeys::test_invalid_date_key", "tests/unit/test_models.py::TestDimensionKeys::test_invalid_time_key", "tests/unit/test_models.py::TestDimensionKeys::test_valid_dimension_keys"]