"""
Event models for agent events with hierarchical structure.

This module defines a single AgentEvent model that preserves the XML hierarchy
using a flexible event_data dictionary approach.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any

from pydantic import BaseModel, Field, field_validator, model_validator


class EventType(str, Enum):
    """Supported agent event types."""

    LOGIN = "Login"
    LOGOUT = "Logout"
    AGENT_AVAILABLE = "AgentAvailable"
    AGENT_BUSIED_OUT = "AgentBusiedOut"
    ACD_LOGIN = "ACDLogin"
    ACD_LOGOUT = "ACDLogout"


class AgentEvent(BaseModel):
    """
    Unified model for all agent events with hierarchical event data.

    This model handles all agent event types using a flexible event_data
    dictionary that preserves the XML hierarchy without flattening.
    """

    # Core event fields (required for all events)
    timestamp: datetime = Field(..., description="Event timestamp in UTC")
    event_type: EventType = Field(
        ..., alias="eventType", description="Type of agent event"
    )
    agency_or_element: str = Field(
        ..., alias="agencyOrElement", description="Tenant identifier"
    )
    agent: str = Field(..., description="Agent username/identifier")

    # Event-specific data (preserves XML hierarchy)
    event_data: Dict[str, Any] = Field(
        ..., description="Event-specific data from XML sub-elements"
    )

    # SQS metadata (populated by Lambda)
    sqs_message_id: Optional[str] = Field(
        None, description="SQS message ID"
    )
    sqs_receipt_handle: Optional[str] = Field(
        None, description="SQS receipt handle"
    )

    @field_validator("timestamp", mode="before")
    @classmethod
    def parse_timestamp(cls, v):
        """Parse timestamp from string if needed."""
        if isinstance(v, str):
            # Handle ISO format with timezone
            if v.endswith("Z"):
                v = v[:-1] + "+00:00"
            return datetime.fromisoformat(v)
        return v

    @model_validator(mode="after")
    def validate_acd_requirements(self):
        """Validate that ACD events have required ring group information."""
        if self.event_type in [EventType.ACD_LOGIN, EventType.ACD_LOGOUT]:
            ring_group_name = self.get_value("ringGroupName")
            if not ring_group_name:
                raise ValueError(
                    f"{self.event_type} events must include ringGroupName"
                )
        return self

    def get_value(self, key: str, default: Any = None) -> Any:
        """
        Get a value from the event_data dictionary.

        Args:
            key: The key to look for in event_data
            default: Default value if key not found

        Returns:
            The value from event_data or default
        """
        return self.event_data.get(key, default)

    def get_agent_uri(self) -> Optional[str]:
        """Get agent URI from event data."""
        # Try both 'uri' and 'agentUri' (ACD events use agentUri)
        return self.get_value("uri") or self.get_value("agentUri")

    def get_ring_group_name(self) -> Optional[str]:
        """Get ring group name for ACD events."""
        return self.get_value("ringGroupName")

    def get_media_label(self) -> Optional[str]:
        """Get media label from event data."""
        return self.get_value("mediaLabel")

    def get_operator_id(self) -> Optional[str]:
        """Get operator ID from event data."""
        return self.get_value("operatorId")

    def get_workstation(self) -> Optional[str]:
        """Get workstation from event data."""
        return self.get_value("workstation")

    def get_device_name(self) -> Optional[str]:
        """Get device name from event data."""
        return self.get_value("deviceName")

    def get_voice_qos(self) -> Optional[Dict[str, Any]]:
        """Get voice quality metrics for logout events."""
        return self.get_value("voiceQOS")

    def has_voice_qos(self) -> bool:
        """Check if event has voice quality metrics."""
        return self.get_voice_qos() is not None

    model_config = {
        "populate_by_name": True,
        "str_strip_whitespace": True,
        "validate_assignment": True,
        "extra": "forbid",
    }
