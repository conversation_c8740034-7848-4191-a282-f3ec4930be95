# PowerShell test script for Agent Event Processor Lambda
# This script sends test messages to SQS and tests the Lambda function

$ErrorActionPreference = "Stop"

# Configuration
$LOCALSTACK_ENDPOINT = "http://localhost:4566"
$AWS_REGION = "us-east-1"
$QUEUE_NAME = "agent-events-queue"

Write-Host "Testing Agent Event Processor Lambda..."

# Configure AWS CLI for LocalStack
$env:AWS_ACCESS_KEY_ID = "test"
$env:AWS_SECRET_ACCESS_KEY = "test"
$env:AWS_DEFAULT_REGION = $AWS_REGION

# Get queue URL
$QUEUE_URL = aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs get-queue-url --queue-name $QUEUE_NAME --region $AWS_REGION --query 'QueueUrl' --output text

Write-Host "Queue URL: $QUEUE_URL"

# Test XML message
$TEST_XML = @"
<LogEvent xmlns="http://solacom.com/Logging">
<timestamp>2025-02-05T11:44:18.031Z</timestamp>
<agencyOrElement>Brandon911</agencyOrElement>
<agent>test-agent</agent>
<eventType>Login</eventType>
<login>
<mediaLabel>_ML_194D5ECDE50C0001C46A@BrandonMB</mediaLabel>
<uri>tel:+2045553006</uri>
<agentRole>Rural - CT</agentRole>
<reason>normal</reason>
</login>
</LogEvent>
"@

# Create SQS message body
$message_body = @{
    Message = $TEST_XML
} | ConvertTo-Json -Compress

Write-Host "Sending test message to SQS..."
aws --endpoint-url=$LOCALSTACK_ENDPOINT sqs send-message --queue-url $QUEUE_URL --message-body $message_body --region $AWS_REGION

Write-Host "Test message sent!"

# Test the Lambda function directly by invoking it
Write-Host "Testing Lambda function directly..."

# Create Lambda event payload
$lambda_payload = @{
    Records = @(
        @{
            messageId = "test-message-id-1"
            receiptHandle = "test-receipt-handle-1"
            body = $message_body
            attributes = @{
                ApproximateReceiveCount = "1"
                SentTimestamp = "1640995200000"
                SenderId = "AIDAIENQZJOLO23YVJ4VO"
                ApproximateFirstReceiveTimestamp = "1640995200000"
            }
            messageAttributes = @{}
            md5OfBody = "test-md5"
            eventSource = "aws:sqs"
            eventSourceARN = "arn:aws:sqs:us-east-1:123456789012:test-queue"
            awsRegion = "us-east-1"
        }
    )
} | ConvertTo-Json -Depth 10 -Compress

# Test the Lambda container directly
Write-Host "Testing Lambda container directly..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:9000/2015-03-31/functions/function/invocations" -Method POST -Body $lambda_payload -ContentType "application/json" -UseBasicParsing
    Write-Host "Lambda Response Status: $($response.StatusCode)"
    Write-Host "Lambda Response Body: $($response.Content)"
}
catch {
    Write-Host "Lambda container test failed: $($_.Exception.Message)"
    Write-Host "This is expected if the Lambda container is not running in standalone mode"
}

Write-Host ""
Write-Host "Test completed!"
Write-Host ""
Write-Host "To check the database, connect to PostgreSQL:"
Write-Host "Host: localhost"
Write-Host "Port: 5432"
Write-Host "Database: test_analytics"
Write-Host "User: test_user"
Write-Host "Password: test_password"
Write-Host ""
Write-Host "To check LocalStack logs:"
Write-Host "docker logs agent-event-processor-localstack"
Write-Host ""
Write-Host "To check Lambda logs:"
Write-Host "docker logs agent-event-processor-lambda"
