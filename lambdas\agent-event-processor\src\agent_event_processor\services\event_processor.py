"""
Event processor service for handling agent events.

This module provides the main business logic for processing agent events,
including validation, transformation, and database operations.
"""

import time
from typing import Dict, Any

from aws_lambda_powertools import Logger, Tracer, Metrics
from aws_lambda_powertools.metrics import MetricUnit
from aws_lambda_typing import context as LambdaContext

from ..config.settings import Settings
from ..models.events import AgentEvent
from ..models.database import DimensionKeys
from ..utils.timezone_utils import (
    get_tenant_timezone,
    convert_to_tenant_timezone,
    generate_dimension_keys,
)
from .database_service import DatabaseService, DimensionTableManager, FactTableManager

# Initialize PowerTools components
logger = Logger()
tracer = Tracer()
metrics = Metrics()


class EventProcessor:
    """
    Main event processor for agent events.

    This class orchestrates the complete processing pipeline from SQS message
    parsing through database operations and metrics publishing.
    """

    def __init__(self, settings: Settings):
        """
        Initialize event processor.

        Args:
            settings: Application configuration settings.
        """
        self.settings = settings

        # Initialize services
        self.database_service = DatabaseService(settings.database)
        self.dimension_manager = DimensionTableManager(self.database_service)
        self.fact_manager = FactTableManager(self.database_service)

        logger.info("Event processor initialized")

    @tracer.capture_method
    def process_single_event(self, event: AgentEvent, context: LambdaContext) -> None:
        """
        Process a single agent event through the complete pipeline.

        Args:
            event: Validated agent event to process
            context: Lambda execution context

        Raises:
            Exception: If processing fails at any stage
        """
        start_time = time.time()

        try:
            # Step 1: Resolve dimension keys
            dimension_keys = self._resolve_dimension_keys(event)

            # Step 2: Transform event data
            fact_data = self._transform_to_fact_data(event, dimension_keys)

            # Step 3: Insert into database
            self._insert_fact_record(fact_data)

            # Record success metrics
            processing_time = (time.time() - start_time) * 1000
            metrics.add_metric(
                name="EventProcessingTime",
                unit=MetricUnit.Milliseconds,
                value=processing_time
            )
            metrics.add_metric(
                name="EventProcessed",
                unit=MetricUnit.Count,
                value=1
            )
            
            logger.info(
                "Event processed successfully",
                event_type=event.event_type,
                agent=event.agent,
                agency=event.agency_or_element,
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            # Record failure metrics
            metrics.add_metric(
                name="EventProcessingError",
                unit=MetricUnit.Count,
                value=1
            )
            
            logger.error(
                "Event processing failed",
                event_type=event.event_type,
                agent=event.agent,
                agency=event.agency_or_element,
                error=str(e),
                exc_info=True
            )
            raise

    @tracer.capture_method
    def _resolve_dimension_keys(self, event: AgentEvent) -> DimensionKeys:
        """
        Resolve dimension keys for the event.

        Args:
            event: Agent event to resolve dimensions for

        Returns:
            DimensionKeys with resolved dimension IDs

        Raises:
            Exception: If dimension resolution fails
        """
        try:
            # Get tenant timezone for date calculations
            tenant_timezone = get_tenant_timezone(event.agency_or_element)
            
            # Convert timestamp to tenant timezone
            tenant_timestamp = convert_to_tenant_timezone(
                event.timestamp, tenant_timezone
            )
            
            # Generate date and time dimension keys
            # Use UTC time for time_key to match dimension table, local time for date_key
            date_key, _ = generate_dimension_keys(tenant_timestamp)
            _, time_key = generate_dimension_keys(event.timestamp)  # UTC time for time dimension

            # Resolve tenant dimension
            tenant_key = self.dimension_manager.get_or_create_tenant_key(
                tenant_name=event.agency_or_element,
                timezone_name=str(tenant_timezone)
            )

            # Resolve agent dimension
            agent_data = {
                'agent_name': event.agent,
                'operator_id': getattr(event.event_data, 'operatorId', None),
                'agent_role': getattr(event.event_data, 'agentRole', None),
                'agent_uri': getattr(event.event_data, 'uri', None),
                'workstation': getattr(event.event_data, 'workstation', None),
            }

            agent_key = self.dimension_manager.upsert_agent_dimension(
                agent_data=agent_data,
                tenant_key=tenant_key
            )

            # Resolve queue dimension if ring group information is available
            queue_key = None
            ring_group_name = event.event_data.get('ringGroupName')
            ring_group_uri = event.event_data.get('ringGroupUri')

            if ring_group_name:
                queue_data = {
                    'ring_group_name': ring_group_name,
                    'ring_group_uri': ring_group_uri,
                }
                queue_key = self.dimension_manager.get_or_create_queue_key(
                    queue_data=queue_data,
                    tenant_key=tenant_key
                )

            # Create resolved dimension keys
            resolved_keys = DimensionKeys(
                tenant_key=tenant_key,
                agent_key=agent_key,
                date_key=date_key,
                time_key=time_key,
                queue_key=queue_key,
            )
            
            logger.debug(
                "Dimension keys resolved",
                event_type=event.event_type,
                agent=event.agent,
                dimension_keys=resolved_keys.model_dump()
            )
            
            return resolved_keys
            
        except Exception as e:
            logger.error(
                "Failed to resolve dimension keys",
                event_type=event.event_type,
                agent=event.agent,
                error=str(e),
                exc_info=True
            )
            raise

    @tracer.capture_method
    def _transform_to_fact_data(
        self, event: AgentEvent, dimension_keys: DimensionKeys
    ) -> Dict[str, Any]:
        """
        Transform event and dimension keys into fact table data.

        Args:
            event: Agent event to transform
            dimension_keys: Resolved dimension keys

        Returns:
            Dictionary containing fact table data
        """
        # Convert timestamp to tenant timezone for local timestamp
        event_timestamp_local = convert_to_tenant_timezone(
            event.timestamp,
            get_tenant_timezone(event.agency_or_element)
        )

        # Base fact data
        fact_data = {
            # Dimension foreign keys
            "tenant_key": dimension_keys.tenant_key,
            "agent_key": dimension_keys.agent_key,
            "date_key": dimension_keys.date_key,
            "time_key": dimension_keys.time_key,

            # Event timestamps
            "event_timestamp_utc": event.timestamp,
            "event_timestamp_local": event_timestamp_local,
            "event_type": event.event_type,

            # Extract all available event data fields
            "reason_code": event.event_data.get('reason'),
            "media_label": event.event_data.get('mediaLabel'),
            "workstation": event.event_data.get('workstation'),
            "device_name": event.event_data.get('deviceName'),
        }

        # Add busied out specific fields
        if event.event_type in ['AgentAvailable', 'AgentBusiedOut']:
            fact_data["busied_out_action"] = event.event_data.get('action')
            fact_data["busied_out_duration"] = event.event_data.get('duration')

        # Add queue key if available
        if dimension_keys.queue_key:
            fact_data["queue_key"] = dimension_keys.queue_key

        logger.debug(
            "Event transformed to fact data",
            event_type=event.event_type,
            agent=event.agent,
            fact_keys=list(fact_data.keys())
        )

        return fact_data

    @tracer.capture_method
    def _insert_fact_record(self, fact_data: Dict[str, Any]) -> None:
        """
        Insert fact record into the database.

        Args:
            fact_data: Fact table data to insert

        Raises:
            Exception: If database insertion fails
        """
        try:
            success = self.fact_manager.insert_agent_event(fact_data)

            if success:
                logger.debug(
                    "Fact record inserted successfully",
                    table="fact_agent_event",
                    event_type=fact_data.get('event_type'),
                    agent_key=fact_data.get('agent_key')
                )
            else:
                logger.info(
                    "Fact record was duplicate - skipped",
                    table="fact_agent_event",
                    event_type=fact_data.get('event_type'),
                    agent_key=fact_data.get('agent_key')
                )

        except Exception as e:
            logger.error(
                "Failed to insert fact record",
                error=str(e),
                fact_data=fact_data,
                exc_info=True
            )
            raise
